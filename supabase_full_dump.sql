

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."create_admin_auth_user"("admin_email" "text", "admin_first_name" "text" DEFAULT 'Admin'::"text", "admin_last_name" "text" DEFAULT 'User'::"text") RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  user_id UUID;
  member_id UUID;
BEGIN
  -- First check if the user already exists in auth.users
  SELECT id INTO user_id FROM auth.users WHERE email = admin_email LIMIT 1;
  
  IF user_id IS NULL THEN
    -- Create the user in auth.users directly (bypassing the API)
    INSERT INTO auth.users (
      email,
      raw_user_meta_data,
      created_at,
      updated_at,
      email_confirmed_at,
      confirmation_sent_at,
      is_super_admin,
      role_id
    ) VALUES (
      admin_email,
      jsonb_build_object('first_name', admin_first_name, 'last_name', admin_last_name),
      NOW(),
      NOW(),
      NOW(), -- Email already confirmed
      NOW(),
      FALSE,
      (SELECT id FROM auth.roles WHERE name = 'authenticated')
    )
    RETURNING id INTO user_id;
  END IF;
  
  -- Now check if there's a corresponding member record
  SELECT id INTO member_id FROM members WHERE id = user_id OR email = admin_email LIMIT 1;
  
  IF member_id IS NULL THEN
    -- Create the member record
    INSERT INTO members (
      id,
      email,
      first_name,
      last_name,
      role,
      role_level,
      status,
      created_at,
      updated_at
    ) VALUES (
      user_id,
      admin_email,
      admin_first_name,
      admin_last_name,
      'admin',
      4,
      'active',
      NOW(),
      NOW()
    )
    RETURNING id INTO member_id;
  ELSE
    -- Update the member record to ensure it has admin privileges
    UPDATE members
    SET 
      role = 'admin',
      role_level = 4,
      status = 'active',
      updated_at = NOW()
    WHERE id = member_id;
  END IF;
  
  RETURN 'Admin user created/updated successfully with ID: ' || user_id::TEXT;
END;
$$;


ALTER FUNCTION "public"."create_admin_auth_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_admin_user"("admin_email" "text", "admin_first_name" "text" DEFAULT 'Admin'::"text", "admin_last_name" "text" DEFAULT 'User'::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  admin_id UUID;
BEGIN
  -- Generate a new UUID
  admin_id := gen_random_uuid();
  
  -- Insert admin record
  INSERT INTO members (
    id,
    email,
    first_name,
    last_name,
    role,
    role_level,
    status,
    created_at,
    updated_at
  ) VALUES (
    admin_id,
    admin_email,
    admin_first_name,
    admin_last_name,
    'admin',
    4,
    'active',
    NOW(),
    NOW()
  );
  
  RETURN admin_id;
END;
$$;


ALTER FUNCTION "public"."create_admin_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_admin_user_manual"("user_id" "uuid", "user_email" "text", "first_name" "text" DEFAULT ''::"text", "last_name" "text" DEFAULT ''::"text") RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Cek apakah member sudah ada
  IF EXISTS (SELECT 1 FROM public.members WHERE id = user_id) THEN
    RETURN 'Member already exists';
  END IF;

  -- Insert member record
  INSERT INTO public.members (
    id,
    email,
    first_name,
    last_name,
    role,
    role_level,
    status,
    created_at,
    updated_at
  ) VALUES (
    user_id,
    user_email,
    first_name,
    last_name,
    'admin',
    4,
    'active',
    NOW(),
    NOW()
  );

  RETURN 'Admin user created successfully';
END;
$$;


ALTER FUNCTION "public"."create_admin_user_manual"("user_id" "uuid", "user_email" "text", "first_name" "text", "last_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_member_from_auth"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Cek apakah member sudah ada
  IF EXISTS (SELECT 1 FROM public.members WHERE id = NEW.id) THEN
    RETURN NEW;
  END IF;

  -- Coba insert dengan error handling
  BEGIN
    INSERT INTO public.members (
      id,
      email,
      first_name,
      last_name,
      role,
      role_level,
      status,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
      COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
      'admin',
      4,
      'active',
      NOW(),
      NOW()
    );
  EXCEPTION WHEN OTHERS THEN
    -- Log error tapi jangan gagalkan trigger
    RAISE NOTICE 'Error creating member from auth user: %', SQLERRM;
  END;

  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."create_member_from_auth"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."generate_member_token"("member_id_param" "uuid", "days_valid" integer DEFAULT 30) RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  new_token TEXT;
BEGIN
  -- Check if member exists
  IF NOT EXISTS (SELECT 1 FROM members WHERE id = member_id_param) THEN
    RAISE EXCEPTION 'Member not found';
  END IF;
  
  -- Generate a random token
  new_token := encode(gen_random_bytes(32), 'hex');
  
  -- Insert the token
  INSERT INTO member_tokens (
    member_id,
    token,
    expires_at
  ) VALUES (
    member_id_param,
    new_token,
    NOW() + (days_valid || ' days')::INTERVAL
  );
  
  RETURN new_token;
END;
$$;


ALTER FUNCTION "public"."generate_member_token"("member_id_param" "uuid", "days_valid" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."invalidate_member_tokens"("member_id_param" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  UPDATE member_tokens
  SET is_active = FALSE
  WHERE member_id = member_id_param;
END;
$$;


ALTER FUNCTION "public"."invalidate_member_tokens"("member_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_admin"() RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN (
    EXISTS (
      SELECT 1 FROM members
      WHERE id = auth.uid() AND (role = 'admin' OR role_level >= 3)
    )
  );
END;
$$;


ALTER FUNCTION "public"."is_admin"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_valid_member_token"("token_value" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  member_id UUID;
BEGIN
  -- Get member_id from token if valid and not expired
  SELECT mt.member_id INTO member_id
  FROM member_tokens mt
  WHERE 
    mt.token = token_value AND
    mt.is_active = TRUE AND
    mt.expires_at > NOW();
    
  -- Update last_used_at if token is valid
  IF member_id IS NOT NULL THEN
    UPDATE member_tokens
    SET last_used_at = NOW()
    WHERE token = token_value;
  END IF;
  
  RETURN member_id;
END;
$$;


ALTER FUNCTION "public"."is_valid_member_token"("token_value" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_cell_group_leaders"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  cg RECORD;
BEGIN
  FOR cg IN SELECT id, leader_id, assistant_leader_id FROM cell_groups
  LOOP
    -- Delete existing leader entries for this cell group
    DELETE FROM cell_group_leaders WHERE cell_group_id = cg.id;
    
    -- Add leader if specified
    IF cg.leader_id IS NOT NULL THEN
      INSERT INTO cell_group_leaders (cell_group_id, member_id, role)
      VALUES (cg.id, cg.leader_id, 'leader')
      ON CONFLICT (cell_group_id, member_id) DO NOTHING;
    END IF;
    
    -- Add assistant leader if specified
    IF cg.assistant_leader_id IS NOT NULL THEN
      INSERT INTO cell_group_leaders (cell_group_id, member_id, role)
      VALUES (cg.id, cg.assistant_leader_id, 'assistant')
      ON CONFLICT (cell_group_id, member_id) DO NOTHING;
    END IF;
  END LOOP;
END;
$$;


ALTER FUNCTION "public"."sync_cell_group_leaders"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_members_to_cell_group_members"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  m RECORD;
BEGIN
  FOR m IN SELECT id, cell_group_id FROM members WHERE cell_group_id IS NOT NULL
  LOOP
    -- Add member to cell_group_members if not already there
    INSERT INTO cell_group_members (cell_group_id, member_id)
    VALUES (m.cell_group_id, m.id)
    ON CONFLICT (cell_group_id, member_id) DO NOTHING;
  END LOOP;
END;
$$;


ALTER FUNCTION "public"."sync_members_to_cell_group_members"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_cell_group_leaders"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Delete existing leader entries for this cell group
  DELETE FROM cell_group_leaders WHERE cell_group_id = NEW.id;
  
  -- Add leader if specified
  IF NEW.leader_id IS NOT NULL THEN
    INSERT INTO cell_group_leaders (cell_group_id, member_id, role)
    VALUES (NEW.id, NEW.leader_id, 'leader')
    ON CONFLICT (cell_group_id, member_id) DO NOTHING;
  END IF;
  
  -- Add assistant leader if specified
  IF NEW.assistant_leader_id IS NOT NULL THEN
    INSERT INTO cell_group_leaders (cell_group_id, member_id, role)
    VALUES (NEW.id, NEW.assistant_leader_id, 'assistant')
    ON CONFLICT (cell_group_id, member_id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_cell_group_leaders"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_cell_group_members"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- If cell_group_id is being set to NULL, remove from cell_group_members
  IF OLD.cell_group_id IS NOT NULL AND NEW.cell_group_id IS NULL THEN
    DELETE FROM cell_group_members 
    WHERE cell_group_id = OLD.cell_group_id AND member_id = NEW.id;
  END IF;
  
  -- If cell_group_id is being changed or set
  IF NEW.cell_group_id IS NOT NULL AND (OLD.cell_group_id IS NULL OR OLD.cell_group_id != NEW.cell_group_id) THEN
    -- Remove from old cell group if any
    IF OLD.cell_group_id IS NOT NULL THEN
      DELETE FROM cell_group_members 
      WHERE cell_group_id = OLD.cell_group_id AND member_id = NEW.id;
    END IF;
    
    -- Add to new cell group
    INSERT INTO cell_group_members (cell_group_id, member_id)
    VALUES (NEW.cell_group_id, NEW.id)
    ON CONFLICT (cell_group_id, member_id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_cell_group_members"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."article_bookmarks" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "article_id" "uuid",
    "member_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."article_bookmarks" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."article_categories" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "icon" "text"
);


ALTER TABLE "public"."article_categories" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."article_comments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "article_id" "uuid",
    "member_id" "uuid",
    "content" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "parent_id" "uuid"
);


ALTER TABLE "public"."article_comments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."articles" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "summary" "text",
    "image_url" "text",
    "published_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "author_id" "uuid",
    "category" "text" NOT NULL,
    "status" "text" DEFAULT 'draft'::"text",
    "featured" boolean DEFAULT false,
    "view_count" integer DEFAULT 0
);


ALTER TABLE "public"."articles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."attendance_meetings" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cell_group_id" "uuid",
    "meeting_date" "date" NOT NULL,
    "meeting_type" character varying(50) DEFAULT 'regular'::character varying,
    "topic" character varying(255),
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "created_by" "uuid",
    "location" character varying(255),
    "offering" double precision,
    "ministry_id" "uuid",
    "event_category" character varying(50) DEFAULT 'cell_group'::character varying,
    "is_realtime" boolean DEFAULT false,
    "end_time" time without time zone,
    "event_date" "date",
    "start_time" time without time zone,
    "event_type" "text",
    "leader_id" "uuid",
    "title" "text"
);


ALTER TABLE "public"."attendance_meetings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."attendance_participants" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "meeting_id" "uuid",
    "member_id" "uuid",
    "status" character varying(20) DEFAULT 'present'::character varying NOT NULL,
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."attendance_participants" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."attendance_visitors" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "meeting_id" "uuid",
    "first_name" character varying(255) NOT NULL,
    "last_name" character varying(255) NOT NULL,
    "phone" character varying(50),
    "email" character varying(255),
    "address" "text",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "converted_to_member_id" "uuid",
    "converted_at" timestamp with time zone
);


ALTER TABLE "public"."attendance_visitors" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cell_group_leaders" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cell_group_id" "uuid" NOT NULL,
    "member_id" "uuid" NOT NULL,
    "role" character varying(50) DEFAULT 'leader'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."cell_group_leaders" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cell_group_members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cell_group_id" "uuid" NOT NULL,
    "member_id" "uuid" NOT NULL,
    "joined_date" "date" DEFAULT CURRENT_DATE,
    "status" character varying(50) DEFAULT 'active'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."cell_group_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cell_groups" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "district_id" "uuid",
    "leader_id" "uuid",
    "assistant_leader_id" "uuid",
    "meeting_day" "text",
    "meeting_time" "text",
    "meeting_location" "text",
    "status" "text" DEFAULT 'active'::"text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "location" "text"
);


ALTER TABLE "public"."cell_groups" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."class_enrollments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "member_id" "uuid" NOT NULL,
    "level_id" "uuid",
    "enrollment_date" "date" DEFAULT CURRENT_DATE,
    "status" character varying(50) DEFAULT 'enrolled'::character varying,
    "completion_date" "date",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "class_id" "uuid" NOT NULL
);


ALTER TABLE "public"."class_enrollments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."class_levels" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "class_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "prerequisite_level_id" "uuid",
    "order_number" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."class_levels" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."class_sessions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "level_id" "uuid",
    "title" character varying(255) NOT NULL,
    "description" "text",
    "session_date" "date" NOT NULL,
    "start_time" time without time zone NOT NULL,
    "end_time" time without time zone NOT NULL,
    "location" character varying(255),
    "instructor_id" "uuid",
    "materials" "jsonb",
    "order_number" integer NOT NULL,
    "attendance_meeting_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "class_id" "uuid"
);


ALTER TABLE "public"."class_sessions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."classes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "category" character varying(50) NOT NULL,
    "max_students" integer,
    "status" character varying(50) DEFAULT 'upcoming'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "has_levels" boolean DEFAULT true
);


ALTER TABLE "public"."classes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."districts" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "leader1_id" "uuid",
    "leader2_id" "uuid",
    "status" "text" DEFAULT 'active'::"text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."districts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."member_tokens" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "member_id" "uuid",
    "token" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone NOT NULL,
    "last_used_at" timestamp with time zone,
    "is_active" boolean DEFAULT true
);


ALTER TABLE "public"."member_tokens" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "email" "text",
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "phone" "text",
    "address" "text",
    "date_of_birth" "date",
    "gender" "text",
    "marital_status" "text",
    "join_date" "date" DEFAULT CURRENT_DATE,
    "emergency_contact_name" "text",
    "emergency_contact_phone" "text",
    "notes" "text",
    "status" "text" DEFAULT 'active'::"text",
    "role" "text" DEFAULT 'member'::"text",
    "role_level" integer DEFAULT 1,
    "role_context" "jsonb",
    "cell_group_id" "uuid",
    "district_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "password_hash" "text",
    "password_reset_required" boolean DEFAULT true,
    "last_password_change" timestamp with time zone
);


ALTER TABLE "public"."members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."ministries" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "leader_id" "uuid",
    "status" character varying(50) DEFAULT 'active'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."ministries" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."ministry_members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "ministry_id" "uuid",
    "member_id" "uuid",
    "role" character varying(255),
    "joined_date" "date" DEFAULT CURRENT_DATE,
    "status" character varying(50) DEFAULT 'active'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."ministry_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."password_reset_tokens" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "member_id" "uuid" NOT NULL,
    "token" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone NOT NULL,
    "used" boolean DEFAULT false
);


ALTER TABLE "public"."password_reset_tokens" OWNER TO "postgres";


ALTER TABLE ONLY "public"."article_bookmarks"
    ADD CONSTRAINT "article_bookmarks_article_id_member_id_key" UNIQUE ("article_id", "member_id");



ALTER TABLE ONLY "public"."article_bookmarks"
    ADD CONSTRAINT "article_bookmarks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."article_categories"
    ADD CONSTRAINT "article_categories_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."article_categories"
    ADD CONSTRAINT "article_categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."article_comments"
    ADD CONSTRAINT "article_comments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles"
    ADD CONSTRAINT "articles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."attendance_meetings"
    ADD CONSTRAINT "attendance_meetings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."attendance_participants"
    ADD CONSTRAINT "attendance_participants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."attendance_visitors"
    ADD CONSTRAINT "attendance_visitors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cell_group_leaders"
    ADD CONSTRAINT "cell_group_leaders_cell_group_id_member_id_key" UNIQUE ("cell_group_id", "member_id");



ALTER TABLE ONLY "public"."cell_group_leaders"
    ADD CONSTRAINT "cell_group_leaders_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cell_group_members"
    ADD CONSTRAINT "cell_group_members_cell_group_id_member_id_key" UNIQUE ("cell_group_id", "member_id");



ALTER TABLE ONLY "public"."cell_group_members"
    ADD CONSTRAINT "cell_group_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cell_groups"
    ADD CONSTRAINT "cell_groups_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."class_enrollments"
    ADD CONSTRAINT "class_enrollments_member_id_level_id_key" UNIQUE ("member_id", "level_id");



ALTER TABLE ONLY "public"."class_enrollments"
    ADD CONSTRAINT "class_enrollments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."class_levels"
    ADD CONSTRAINT "class_levels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."class_sessions"
    ADD CONSTRAINT "class_sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."classes"
    ADD CONSTRAINT "classes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."districts"
    ADD CONSTRAINT "districts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."member_tokens"
    ADD CONSTRAINT "member_tokens_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."member_tokens"
    ADD CONSTRAINT "member_tokens_token_key" UNIQUE ("token");



ALTER TABLE ONLY "public"."members"
    ADD CONSTRAINT "members_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."members"
    ADD CONSTRAINT "members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."ministries"
    ADD CONSTRAINT "ministries_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."ministry_members"
    ADD CONSTRAINT "ministry_members_ministry_id_member_id_key" UNIQUE ("ministry_id", "member_id");



ALTER TABLE ONLY "public"."ministry_members"
    ADD CONSTRAINT "ministry_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."password_reset_tokens"
    ADD CONSTRAINT "password_reset_tokens_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_attendance_meetings_cell_group_id" ON "public"."attendance_meetings" USING "btree" ("cell_group_id");



CREATE INDEX "idx_attendance_participants_meeting_id" ON "public"."attendance_participants" USING "btree" ("meeting_id");



CREATE INDEX "idx_attendance_participants_member_id" ON "public"."attendance_participants" USING "btree" ("member_id");



CREATE INDEX "idx_cell_group_leaders_cell_group_id" ON "public"."cell_group_leaders" USING "btree" ("cell_group_id");



CREATE INDEX "idx_cell_group_leaders_member_id" ON "public"."cell_group_leaders" USING "btree" ("member_id");



CREATE INDEX "idx_cell_group_members_cell_group_id" ON "public"."cell_group_members" USING "btree" ("cell_group_id");



CREATE INDEX "idx_cell_group_members_member_id" ON "public"."cell_group_members" USING "btree" ("member_id");



CREATE INDEX "idx_cell_groups_district_id" ON "public"."cell_groups" USING "btree" ("district_id");



CREATE INDEX "idx_member_tokens_member_id" ON "public"."member_tokens" USING "btree" ("member_id");



CREATE INDEX "idx_member_tokens_token" ON "public"."member_tokens" USING "btree" ("token");



CREATE INDEX "idx_members_cell_group_id" ON "public"."members" USING "btree" ("cell_group_id");



CREATE INDEX "idx_members_district_id" ON "public"."members" USING "btree" ("district_id");



CREATE INDEX "idx_members_email" ON "public"."members" USING "btree" ("email");



CREATE INDEX "idx_members_role" ON "public"."members" USING "btree" ("role");



CREATE INDEX "idx_members_role_level" ON "public"."members" USING "btree" ("role_level");



CREATE INDEX "idx_ministry_members_member_id" ON "public"."ministry_members" USING "btree" ("member_id");



CREATE INDEX "idx_ministry_members_ministry_id" ON "public"."ministry_members" USING "btree" ("ministry_id");



CREATE INDEX "password_reset_tokens_member_id_idx" ON "public"."password_reset_tokens" USING "btree" ("member_id");



CREATE INDEX "password_reset_tokens_token_idx" ON "public"."password_reset_tokens" USING "btree" ("token");



CREATE OR REPLACE TRIGGER "set_ministries_updated_at" BEFORE UPDATE ON "public"."ministries" FOR EACH ROW EXECUTE FUNCTION "public"."set_updated_at"();



CREATE OR REPLACE TRIGGER "set_ministry_members_updated_at" BEFORE UPDATE ON "public"."ministry_members" FOR EACH ROW EXECUTE FUNCTION "public"."set_updated_at"();



CREATE OR REPLACE TRIGGER "trigger_update_cell_group_leaders" AFTER INSERT OR UPDATE OF "leader_id", "assistant_leader_id" ON "public"."cell_groups" FOR EACH ROW EXECUTE FUNCTION "public"."update_cell_group_leaders"();



CREATE OR REPLACE TRIGGER "trigger_update_cell_group_members" AFTER UPDATE OF "cell_group_id" ON "public"."members" FOR EACH ROW EXECUTE FUNCTION "public"."update_cell_group_members"();



ALTER TABLE ONLY "public"."attendance_meetings"
    ADD CONSTRAINT "attendance_meetings_leader_id_fkey" FOREIGN KEY ("leader_id") REFERENCES "public"."members"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."cell_group_leaders"
    ADD CONSTRAINT "cell_group_leaders_cell_group_id_fkey" FOREIGN KEY ("cell_group_id") REFERENCES "public"."cell_groups"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cell_group_leaders"
    ADD CONSTRAINT "cell_group_leaders_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cell_group_members"
    ADD CONSTRAINT "cell_group_members_cell_group_id_fkey" FOREIGN KEY ("cell_group_id") REFERENCES "public"."cell_groups"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cell_group_members"
    ADD CONSTRAINT "cell_group_members_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."class_enrollments"
    ADD CONSTRAINT "class_enrollments_class_id_fkey" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."class_enrollments"
    ADD CONSTRAINT "class_enrollments_level_id_fkey" FOREIGN KEY ("level_id") REFERENCES "public"."class_levels"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."class_enrollments"
    ADD CONSTRAINT "class_enrollments_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."class_levels"
    ADD CONSTRAINT "class_levels_class_id_fkey" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."class_levels"
    ADD CONSTRAINT "class_levels_prerequisite_level_id_fkey" FOREIGN KEY ("prerequisite_level_id") REFERENCES "public"."class_levels"("id");



ALTER TABLE ONLY "public"."class_sessions"
    ADD CONSTRAINT "class_sessions_attendance_meeting_id_fkey" FOREIGN KEY ("attendance_meeting_id") REFERENCES "public"."attendance_meetings"("id");



ALTER TABLE ONLY "public"."class_sessions"
    ADD CONSTRAINT "class_sessions_class_id_fkey" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."class_sessions"
    ADD CONSTRAINT "class_sessions_instructor_id_fkey" FOREIGN KEY ("instructor_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."class_sessions"
    ADD CONSTRAINT "class_sessions_level_id_fkey" FOREIGN KEY ("level_id") REFERENCES "public"."class_levels"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."article_bookmarks"
    ADD CONSTRAINT "fk_article_bookmarks_article" FOREIGN KEY ("article_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."article_bookmarks"
    ADD CONSTRAINT "fk_article_bookmarks_member" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."article_comments"
    ADD CONSTRAINT "fk_article_comments_article" FOREIGN KEY ("article_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."article_comments"
    ADD CONSTRAINT "fk_article_comments_member" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."article_comments"
    ADD CONSTRAINT "fk_article_comments_parent" FOREIGN KEY ("parent_id") REFERENCES "public"."article_comments"("id");



ALTER TABLE ONLY "public"."articles"
    ADD CONSTRAINT "fk_articles_author" FOREIGN KEY ("author_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."attendance_meetings"
    ADD CONSTRAINT "fk_attendance_meetings_cell_group" FOREIGN KEY ("cell_group_id") REFERENCES "public"."cell_groups"("id");



ALTER TABLE ONLY "public"."attendance_meetings"
    ADD CONSTRAINT "fk_attendance_meetings_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."attendance_meetings"
    ADD CONSTRAINT "fk_attendance_meetings_ministry" FOREIGN KEY ("ministry_id") REFERENCES "public"."ministries"("id");



ALTER TABLE ONLY "public"."attendance_participants"
    ADD CONSTRAINT "fk_attendance_participants_meeting" FOREIGN KEY ("meeting_id") REFERENCES "public"."attendance_meetings"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."attendance_participants"
    ADD CONSTRAINT "fk_attendance_participants_member" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."attendance_visitors"
    ADD CONSTRAINT "fk_attendance_visitors_converted_to_member" FOREIGN KEY ("converted_to_member_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."attendance_visitors"
    ADD CONSTRAINT "fk_attendance_visitors_meeting" FOREIGN KEY ("meeting_id") REFERENCES "public"."attendance_meetings"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cell_groups"
    ADD CONSTRAINT "fk_cell_groups_assistant_leader" FOREIGN KEY ("assistant_leader_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."cell_groups"
    ADD CONSTRAINT "fk_cell_groups_district" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id");



ALTER TABLE ONLY "public"."cell_groups"
    ADD CONSTRAINT "fk_cell_groups_leader" FOREIGN KEY ("leader_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."districts"
    ADD CONSTRAINT "fk_districts_leader1" FOREIGN KEY ("leader1_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."districts"
    ADD CONSTRAINT "fk_districts_leader2" FOREIGN KEY ("leader2_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."member_tokens"
    ADD CONSTRAINT "fk_member_tokens_member_id" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."members"
    ADD CONSTRAINT "fk_members_cell_group" FOREIGN KEY ("cell_group_id") REFERENCES "public"."cell_groups"("id");



ALTER TABLE ONLY "public"."members"
    ADD CONSTRAINT "fk_members_district" FOREIGN KEY ("district_id") REFERENCES "public"."districts"("id");



ALTER TABLE ONLY "public"."ministries"
    ADD CONSTRAINT "fk_ministries_leader" FOREIGN KEY ("leader_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."ministry_members"
    ADD CONSTRAINT "fk_ministry_members_member" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id");



ALTER TABLE ONLY "public"."ministry_members"
    ADD CONSTRAINT "fk_ministry_members_ministry" FOREIGN KEY ("ministry_id") REFERENCES "public"."ministries"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."password_reset_tokens"
    ADD CONSTRAINT "password_reset_tokens_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE CASCADE;



CREATE POLICY "Admins can do anything with member_tokens" ON "public"."member_tokens" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can do anything with members" ON "public"."members" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can insert password reset tokens" ON "public"."password_reset_tokens" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "auth"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND (("users"."role")::"text" = 'admin'::"text")))));



CREATE POLICY "Admins can manage article_bookmarks" ON "public"."article_bookmarks" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage article_categories" ON "public"."article_categories" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage article_comments" ON "public"."article_comments" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage articles" ON "public"."articles" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage attendance_meetings" ON "public"."attendance_meetings" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage attendance_participants" ON "public"."attendance_participants" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage attendance_visitors" ON "public"."attendance_visitors" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage cell_group_leaders" ON "public"."cell_group_leaders" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage cell_group_members" ON "public"."cell_group_members" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage cell_groups" ON "public"."cell_groups" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage districts" ON "public"."districts" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage ministries" ON "public"."ministries" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can manage ministry_members" ON "public"."ministry_members" USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Admins can update password reset tokens" ON "public"."password_reset_tokens" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "auth"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND (("users"."role")::"text" = 'admin'::"text")))));



CREATE POLICY "Admins can view password reset tokens" ON "public"."password_reset_tokens" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "auth"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND (("users"."role")::"text" = 'admin'::"text")))));



CREATE POLICY "Allow delete for admins" ON "public"."ministries" FOR DELETE USING (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"())));



CREATE POLICY "Allow delete for admins" ON "public"."ministry_members" FOR DELETE USING (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"())));



CREATE POLICY "Allow insert for admins" ON "public"."ministries" FOR INSERT WITH CHECK (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"())));



CREATE POLICY "Allow insert for admins" ON "public"."ministry_members" FOR INSERT WITH CHECK (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"())));



CREATE POLICY "Allow read access for all authenticated users" ON "public"."ministries" FOR SELECT USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Allow read access for all authenticated users" ON "public"."ministry_members" FOR SELECT USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Allow update for admins" ON "public"."ministries" FOR UPDATE USING (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"()))) WITH CHECK (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"())));



CREATE POLICY "Allow update for admins" ON "public"."ministry_members" FOR UPDATE USING (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"()))) WITH CHECK (( SELECT (("members"."role" = 'admin'::"text") OR ("members"."role_level" >= 4))
   FROM "public"."members"
  WHERE ("members"."id" = "auth"."uid"())));



CREATE POLICY "Anon can update members" ON "public"."members" FOR UPDATE USING (("auth"."role"() = 'anon'::"text")) WITH CHECK (("auth"."role"() = 'anon'::"text"));



CREATE POLICY "Anyone can read active members" ON "public"."members" FOR SELECT USING (("status" = 'active'::"text"));



CREATE POLICY "Anyone can read article_bookmarks" ON "public"."article_bookmarks" FOR SELECT USING (true);



CREATE POLICY "Anyone can read article_categories" ON "public"."article_categories" FOR SELECT USING (true);



CREATE POLICY "Anyone can read article_comments" ON "public"."article_comments" FOR SELECT USING (true);



CREATE POLICY "Anyone can read attendance_meetings" ON "public"."attendance_meetings" FOR SELECT USING (true);



CREATE POLICY "Anyone can read attendance_participants" ON "public"."attendance_participants" FOR SELECT USING (true);



CREATE POLICY "Anyone can read attendance_visitors" ON "public"."attendance_visitors" FOR SELECT USING (true);



CREATE POLICY "Anyone can read cell_group_leaders" ON "public"."cell_group_leaders" FOR SELECT USING (true);



CREATE POLICY "Anyone can read cell_group_members" ON "public"."cell_group_members" FOR SELECT USING (true);



CREATE POLICY "Anyone can read cell_groups" ON "public"."cell_groups" FOR SELECT USING (true);



CREATE POLICY "Anyone can read districts" ON "public"."districts" FOR SELECT USING (true);



CREATE POLICY "Anyone can read ministries" ON "public"."ministries" FOR SELECT USING (true);



CREATE POLICY "Anyone can read ministry_members" ON "public"."ministry_members" FOR SELECT USING (true);



CREATE POLICY "Anyone can read published articles" ON "public"."articles" FOR SELECT USING (("status" = 'published'::"text"));



CREATE POLICY "Authenticated users can insert" ON "public"."class_enrollments" FOR INSERT WITH CHECK (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Authenticated users can read" ON "public"."class_enrollments" FOR SELECT USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable delete for admins" ON "public"."class_levels" FOR DELETE USING ("public"."is_admin"());



CREATE POLICY "Enable delete for authenticated users" ON "public"."class_sessions" FOR DELETE USING (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"])));



CREATE POLICY "Enable delete for authenticated users" ON "public"."classes" FOR DELETE USING (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"])));



CREATE POLICY "Enable insert for authenticated users" ON "public"."class_levels" FOR INSERT WITH CHECK (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"])));



CREATE POLICY "Enable insert for authenticated users" ON "public"."class_sessions" FOR INSERT WITH CHECK (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"])));



CREATE POLICY "Enable insert for authenticated users" ON "public"."classes" FOR INSERT WITH CHECK (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"])));



CREATE POLICY "Enable read access for all users" ON "public"."class_levels" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."class_sessions" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."classes" FOR SELECT USING (true);



CREATE POLICY "Enable update for admins" ON "public"."class_levels" FOR UPDATE USING ("public"."is_admin"()) WITH CHECK ("public"."is_admin"());



CREATE POLICY "Enable update for authenticated users" ON "public"."class_sessions" FOR UPDATE USING (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"])));



CREATE POLICY "Enable update for authenticated users" ON "public"."classes" FOR UPDATE USING (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"]))) WITH CHECK (("auth"."role"() = ANY (ARRAY['authenticated'::"text", 'service_role'::"text"])));



CREATE POLICY "Members can update their own data" ON "public"."members" FOR UPDATE USING (("id" = "auth"."uid"())) WITH CHECK (("id" = "auth"."uid"()));



CREATE POLICY "Members can view their own tokens" ON "public"."member_tokens" FOR SELECT USING (("member_id" = "auth"."uid"()));



CREATE POLICY "Service role bypass" ON "public"."class_enrollments" USING ((("auth"."jwt"() ->> 'role'::"text") = 'service_role'::"text")) WITH CHECK ((("auth"."jwt"() ->> 'role'::"text") = 'service_role'::"text"));



CREATE POLICY "Service role can update members" ON "public"."members" FOR UPDATE USING (("auth"."role"() = 'service_role'::"text")) WITH CHECK (("auth"."role"() = 'service_role'::"text"));



CREATE POLICY "Users can delete their own enrollments" ON "public"."class_enrollments" FOR DELETE USING (((("auth"."uid"())::"text" = ("member_id")::"text") OR ( SELECT "public"."is_admin"() AS "is_admin")));



CREATE POLICY "Users can update their own enrollments" ON "public"."class_enrollments" FOR UPDATE USING (((("auth"."uid"())::"text" = ("member_id")::"text") OR ( SELECT "public"."is_admin"() AS "is_admin")));



ALTER TABLE "public"."article_bookmarks" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."article_categories" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."article_comments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."articles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."attendance_meetings" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."attendance_participants" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."attendance_visitors" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."cell_group_leaders" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."cell_group_members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."cell_groups" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."class_enrollments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."class_levels" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."class_sessions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."classes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."districts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."member_tokens" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."ministries" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."ministry_members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."password_reset_tokens" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";






ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."attendance_meetings";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."attendance_participants";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."attendance_visitors";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";











































































































































































GRANT ALL ON FUNCTION "public"."create_admin_auth_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_admin_auth_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_admin_auth_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_admin_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_admin_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_admin_user"("admin_email" "text", "admin_first_name" "text", "admin_last_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_admin_user_manual"("user_id" "uuid", "user_email" "text", "first_name" "text", "last_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_admin_user_manual"("user_id" "uuid", "user_email" "text", "first_name" "text", "last_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_admin_user_manual"("user_id" "uuid", "user_email" "text", "first_name" "text", "last_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_member_from_auth"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_member_from_auth"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_member_from_auth"() TO "service_role";



GRANT ALL ON FUNCTION "public"."generate_member_token"("member_id_param" "uuid", "days_valid" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."generate_member_token"("member_id_param" "uuid", "days_valid" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_member_token"("member_id_param" "uuid", "days_valid" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."invalidate_member_tokens"("member_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."invalidate_member_tokens"("member_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."invalidate_member_tokens"("member_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_admin"() TO "anon";
GRANT ALL ON FUNCTION "public"."is_admin"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_admin"() TO "service_role";



GRANT ALL ON FUNCTION "public"."is_valid_member_token"("token_value" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."is_valid_member_token"("token_value" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_valid_member_token"("token_value" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_cell_group_leaders"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_cell_group_leaders"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_cell_group_leaders"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_members_to_cell_group_members"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_members_to_cell_group_members"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_members_to_cell_group_members"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_cell_group_leaders"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_cell_group_leaders"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_cell_group_leaders"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_cell_group_members"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_cell_group_members"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_cell_group_members"() TO "service_role";


















GRANT ALL ON TABLE "public"."article_bookmarks" TO "anon";
GRANT ALL ON TABLE "public"."article_bookmarks" TO "authenticated";
GRANT ALL ON TABLE "public"."article_bookmarks" TO "service_role";



GRANT ALL ON TABLE "public"."article_categories" TO "anon";
GRANT ALL ON TABLE "public"."article_categories" TO "authenticated";
GRANT ALL ON TABLE "public"."article_categories" TO "service_role";



GRANT ALL ON TABLE "public"."article_comments" TO "anon";
GRANT ALL ON TABLE "public"."article_comments" TO "authenticated";
GRANT ALL ON TABLE "public"."article_comments" TO "service_role";



GRANT ALL ON TABLE "public"."articles" TO "anon";
GRANT ALL ON TABLE "public"."articles" TO "authenticated";
GRANT ALL ON TABLE "public"."articles" TO "service_role";



GRANT ALL ON TABLE "public"."attendance_meetings" TO "anon";
GRANT ALL ON TABLE "public"."attendance_meetings" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance_meetings" TO "service_role";



GRANT ALL ON TABLE "public"."attendance_participants" TO "anon";
GRANT ALL ON TABLE "public"."attendance_participants" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance_participants" TO "service_role";



GRANT ALL ON TABLE "public"."attendance_visitors" TO "anon";
GRANT ALL ON TABLE "public"."attendance_visitors" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance_visitors" TO "service_role";



GRANT ALL ON TABLE "public"."cell_group_leaders" TO "anon";
GRANT ALL ON TABLE "public"."cell_group_leaders" TO "authenticated";
GRANT ALL ON TABLE "public"."cell_group_leaders" TO "service_role";



GRANT ALL ON TABLE "public"."cell_group_members" TO "anon";
GRANT ALL ON TABLE "public"."cell_group_members" TO "authenticated";
GRANT ALL ON TABLE "public"."cell_group_members" TO "service_role";



GRANT ALL ON TABLE "public"."cell_groups" TO "anon";
GRANT ALL ON TABLE "public"."cell_groups" TO "authenticated";
GRANT ALL ON TABLE "public"."cell_groups" TO "service_role";



GRANT ALL ON TABLE "public"."class_enrollments" TO "anon";
GRANT ALL ON TABLE "public"."class_enrollments" TO "authenticated";
GRANT ALL ON TABLE "public"."class_enrollments" TO "service_role";



GRANT ALL ON TABLE "public"."class_levels" TO "anon";
GRANT ALL ON TABLE "public"."class_levels" TO "authenticated";
GRANT ALL ON TABLE "public"."class_levels" TO "service_role";



GRANT ALL ON TABLE "public"."class_sessions" TO "anon";
GRANT ALL ON TABLE "public"."class_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."class_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."classes" TO "anon";
GRANT ALL ON TABLE "public"."classes" TO "authenticated";
GRANT ALL ON TABLE "public"."classes" TO "service_role";



GRANT ALL ON TABLE "public"."districts" TO "anon";
GRANT ALL ON TABLE "public"."districts" TO "authenticated";
GRANT ALL ON TABLE "public"."districts" TO "service_role";



GRANT ALL ON TABLE "public"."member_tokens" TO "anon";
GRANT ALL ON TABLE "public"."member_tokens" TO "authenticated";
GRANT ALL ON TABLE "public"."member_tokens" TO "service_role";



GRANT ALL ON TABLE "public"."members" TO "anon";
GRANT ALL ON TABLE "public"."members" TO "authenticated";
GRANT ALL ON TABLE "public"."members" TO "service_role";



GRANT ALL ON TABLE "public"."ministries" TO "anon";
GRANT ALL ON TABLE "public"."ministries" TO "authenticated";
GRANT ALL ON TABLE "public"."ministries" TO "service_role";



GRANT ALL ON TABLE "public"."ministry_members" TO "anon";
GRANT ALL ON TABLE "public"."ministry_members" TO "authenticated";
GRANT ALL ON TABLE "public"."ministry_members" TO "service_role";



GRANT ALL ON TABLE "public"."password_reset_tokens" TO "anon";
GRANT ALL ON TABLE "public"."password_reset_tokens" TO "authenticated";
GRANT ALL ON TABLE "public"."password_reset_tokens" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
