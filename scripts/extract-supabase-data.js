const fs = require('fs');
const path = require('path');

// Read the Supabase dump file
const supabaseDumpPath = path.join(__dirname, '../../../Nextjs/churchManagement/supabase_full_dump.sql');

function extractTableData(dumpContent, tableName) {
  console.log(`🔍 Extracting data for table: ${tableName}`);
  
  // Look for COPY statements (PostgreSQL bulk insert format)
  const copyRegex = new RegExp(`COPY public\\.${tableName}[^;]+;`, 'gs');
  const copyMatches = dumpContent.match(copyRegex) || [];
  
  // Look for INSERT statements
  const insertRegex = new RegExp(`INSERT INTO (?:public\\.)?${tableName}[^;]+;`, 'gs');
  const insertMatches = dumpContent.match(insertRegex) || [];
  
  console.log(`📊 Found ${copyMatches.length} COPY statements and ${insertMatches.length} INSERT statements for ${tableName}`);
  
  return {
    copyStatements: copyMatches,
    insertStatements: insertMatches
  };
}

function analyzeSupabaseStructure() {
  console.log('📋 Analyzing Supabase database structure...\n');
  
  if (!fs.existsSync(supabaseDumpPath)) {
    console.error('❌ Supabase dump file not found at:', supabaseDumpPath);
    return null;
  }
  
  const dumpContent = fs.readFileSync(supabaseDumpPath, 'utf8');
  
  // Extract all table names
  const createTableRegex = /CREATE TABLE[^"]*"public"\."([^"]+)"/g;
  const tables = [];
  let match;
  
  while ((match = createTableRegex.exec(dumpContent)) !== null) {
    tables.push(match[1]);
  }
  
  console.log('📋 Found tables in Supabase:');
  tables.forEach(table => console.log(`  - ${table}`));
  
  console.log('\n🔍 Checking for data in key tables...\n');
  
  const keyTables = [
    'members',
    'districts', 
    'cell_groups',
    'cell_group_members',
    'ministries',
    'attendance_meetings',
    'attendance_participants',
    'classes',
    'articles'
  ];
  
  const dataAnalysis = {};
  
  keyTables.forEach(tableName => {
    if (tables.includes(tableName)) {
      const data = extractTableData(dumpContent, tableName);
      dataAnalysis[tableName] = data;
      
      if (data.copyStatements.length > 0 || data.insertStatements.length > 0) {
        console.log(`✅ ${tableName}: Has data to migrate`);
      } else {
        console.log(`⚪ ${tableName}: No data found`);
      }
    } else {
      console.log(`❌ ${tableName}: Table not found in dump`);
    }
  });
  
  return {
    allTables: tables,
    dataAnalysis,
    dumpContent
  };
}

function generateMigrationPlan(analysis) {
  if (!analysis) return;
  
  console.log('\n📋 Migration Plan:\n');
  
  const hasData = Object.entries(analysis.dataAnalysis)
    .filter(([table, data]) => data.copyStatements.length > 0 || data.insertStatements.length > 0)
    .map(([table]) => table);
  
  const noData = Object.entries(analysis.dataAnalysis)
    .filter(([table, data]) => data.copyStatements.length === 0 && data.insertStatements.length === 0)
    .map(([table]) => table);
  
  if (hasData.length > 0) {
    console.log('🔄 Tables with data to migrate:');
    hasData.forEach(table => console.log(`  ✅ ${table}`));
    console.log('\n📝 Recommended approach:');
    console.log('  1. Create custom data extraction script');
    console.log('  2. Parse PostgreSQL data format');
    console.log('  3. Convert to MySQL-compatible format');
    console.log('  4. Use Prisma to insert data');
  } else {
    console.log('⚪ No existing data found in key tables');
    console.log('\n📝 Recommended approach:');
    console.log('  1. Use the sample data seeding script');
    console.log('  2. Start fresh with new data entry');
    console.log('  3. Import data manually if needed');
  }
  
  console.log('\n🚀 To proceed with migration:');
  console.log('  npm run migrate:supabase');
  
  return {
    hasData: hasData.length > 0,
    tablesWithData: hasData,
    tablesWithoutData: noData
  };
}

function main() {
  console.log('🔍 Supabase Data Analysis Tool\n');
  console.log('=' .repeat(50));
  
  const analysis = analyzeSupabaseStructure();
  const plan = generateMigrationPlan(analysis);
  
  // Save analysis to file for reference
  if (analysis) {
    const outputPath = path.join(__dirname, 'supabase-analysis.json');
    fs.writeFileSync(outputPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      tables: analysis.allTables,
      dataAnalysis: Object.fromEntries(
        Object.entries(analysis.dataAnalysis).map(([table, data]) => [
          table,
          {
            hasCopyStatements: data.copyStatements.length > 0,
            hasInsertStatements: data.insertStatements.length > 0,
            copyCount: data.copyStatements.length,
            insertCount: data.insertStatements.length
          }
        ])
      ),
      migrationPlan: plan
    }, null, 2));
    
    console.log(`\n📄 Analysis saved to: ${outputPath}`);
  }
}

if (require.main === module) {
  main();
}

module.exports = { analyzeSupabaseStructure, extractTableData, generateMigrationPlan };
