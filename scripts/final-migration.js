const { createClient } = require('@supabase/supabase-js');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Supabase configuration
const SUPABASE_URL = 'https://hjuogefczikouxbjmubj.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhqdW9nZWZjemlrb3V4YmptdWJqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDk4OTc3MiwiZXhwIjoyMDYwNTY1NzcyfQ.Vq7Ci3IlR7veGU134LkDHbXZadS6ghVQJTIGSGGhCuI';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const prisma = new PrismaClient();

async function pullData() {
  console.log('🔍 Pulling data from Supabase...');
  
  const tables = ['districts', 'members', 'cell_groups', 'cell_group_members', 'cell_group_leaders', 'attendance_meetings', 'attendance_participants', 'attendance_visitors'];
  const data = {};
  
  for (const table of tables) {
    console.log(`📊 Pulling ${table}...`);
    const { data: tableData, error } = await supabase.from(table).select('*');
    
    if (error) {
      console.log(`⚠️  ${table}: ${error.message}`);
      data[table] = [];
    } else {
      data[table] = tableData || [];
      console.log(`✅ ${table}: ${tableData?.length || 0} records`);
    }
  }
  
  return data;
}

async function clearNonDistrictData() {
  console.log('🗑️  Clearing non-district data...');
  
  // Keep districts, clear everything else
  await prisma.attendanceVisitor.deleteMany();
  await prisma.attendanceParticipant.deleteMany();
  await prisma.attendanceMeeting.deleteMany();
  await prisma.cellGroupLeader.deleteMany();
  await prisma.cellGroupMember.deleteMany();
  await prisma.cellGroup.deleteMany();
  await prisma.memberToken.deleteMany();
  await prisma.passwordResetToken.deleteMany();
  await prisma.member.deleteMany();
  
  console.log('✅ Non-district data cleared');
}

async function migrateMembers(members) {
  if (!members || members.length === 0) return;
  
  console.log('👥 Migrating members...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const member of members) {
    try {
      let passwordHash = null;
      if (member.password_hash) {
        passwordHash = member.password_hash;
      } else {
        passwordHash = await bcrypt.hash('password123', 12);
      }
      
      await prisma.member.create({
        data: {
          id: member.id,
          email: member.email,
          first_name: member.first_name,
          last_name: member.last_name,
          phone: member.phone || null,
          address: member.address || null,
          date_of_birth: member.date_of_birth ? new Date(member.date_of_birth) : null,
          gender: member.gender || null,
          marital_status: member.marital_status || null,
          occupation: member.occupation || null,
          emergency_contact_name: member.emergency_contact_name || null,
          emergency_contact_phone: member.emergency_contact_phone || null,
          status: member.status || 'active',
          role: member.role || 'member',
          role_level: member.role_level || 1,
          join_date: member.join_date ? new Date(member.join_date) : new Date(),
          district_id: member.district_id || null,
          cell_group_id: member.cell_group_id || null,
          password_hash: passwordHash,
          created_at: new Date(member.created_at),
          updated_at: new Date(member.updated_at)
        }
      });
      successCount++;
    } catch (error) {
      console.error(`❌ Error migrating member ${member.id}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`✅ Migrated ${successCount} members (${errorCount} errors)`);
}

async function migrateCellGroups(cellGroups) {
  if (!cellGroups || cellGroups.length === 0) return;
  
  console.log('🏠 Migrating cell groups...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const cellGroup of cellGroups) {
    try {
      await prisma.cellGroup.create({
        data: {
          id: cellGroup.id,
          name: cellGroup.name,
          description: cellGroup.description || null,
          district_id: cellGroup.district_id,
          leader_id: cellGroup.leader_id,
          assistant_leader_id: cellGroup.assistant_leader_id,
          meeting_day: cellGroup.meeting_day?.toLowerCase() || null,
          meeting_time: cellGroup.meeting_time || null,
          meeting_location: cellGroup.meeting_location || null,
          status: cellGroup.status || 'active',
          created_at: new Date(cellGroup.created_at),
          updated_at: new Date(cellGroup.updated_at)
        }
      });
      successCount++;
    } catch (error) {
      console.error(`❌ Error migrating cell group ${cellGroup.id}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`✅ Migrated ${successCount} cell groups (${errorCount} errors)`);
}

async function migrateCellGroupMembers(cgMembers) {
  if (!cgMembers || cgMembers.length === 0) return;
  
  console.log('👥 Migrating cell group members...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const cgMember of cgMembers) {
    try {
      await prisma.cellGroupMember.create({
        data: {
          id: cgMember.id,
          cell_group_id: cgMember.cell_group_id,
          member_id: cgMember.member_id,
          joined_date: cgMember.joined_at ? new Date(cgMember.joined_at) : new Date(),
          status: cgMember.status || 'active',
          created_at: new Date(cgMember.created_at),
          updated_at: new Date(cgMember.updated_at)
        }
      });
      successCount++;
    } catch (error) {
      console.error(`❌ Error migrating cell group member ${cgMember.id}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`✅ Migrated ${successCount} cell group members (${errorCount} errors)`);
}

async function migrateCellGroupLeaders(cgLeaders) {
  if (!cgLeaders || cgLeaders.length === 0) return;
  
  console.log('👑 Migrating cell group leaders...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const cgLeader of cgLeaders) {
    try {
      await prisma.cellGroupLeader.create({
        data: {
          id: cgLeader.id,
          cell_group_id: cgLeader.cell_group_id,
          member_id: cgLeader.member_id,
          role: cgLeader.role || 'leader',
          created_at: new Date(cgLeader.created_at),
          updated_at: new Date(cgLeader.updated_at)
        }
      });
      successCount++;
    } catch (error) {
      console.error(`❌ Error migrating cell group leader ${cgLeader.id}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`✅ Migrated ${successCount} cell group leaders (${errorCount} errors)`);
}

async function migrateAttendanceMeetings(meetings) {
  if (!meetings || meetings.length === 0) return;
  
  console.log('📅 Migrating attendance meetings...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const meeting of meetings) {
    try {
      await prisma.attendanceMeeting.create({
        data: {
          id: meeting.id,
          cell_group_id: meeting.cell_group_id,
          meeting_date: new Date(meeting.meeting_date),
          meeting_type: meeting.meeting_type || 'regular',
          topic: meeting.topic || null,
          notes: meeting.notes || null,
          location: meeting.location || null,
          offering: meeting.offering ? parseFloat(meeting.offering) : null,
          created_by: meeting.created_by || null,
          ministry_id: meeting.ministry_id || null,
          event_category: meeting.event_category || null,
          is_realtime: meeting.is_realtime || false,
          created_at: new Date(meeting.created_at),
          updated_at: new Date(meeting.updated_at)
        }
      });
      successCount++;
    } catch (error) {
      console.error(`❌ Error migrating meeting ${meeting.id}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`✅ Migrated ${successCount} attendance meetings (${errorCount} errors)`);
}

async function migrateAttendanceParticipants(participants) {
  if (!participants || participants.length === 0) return;
  
  console.log('✋ Migrating attendance participants...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const participant of participants) {
    try {
      await prisma.attendanceParticipant.create({
        data: {
          id: participant.id,
          meeting_id: participant.meeting_id,
          member_id: participant.member_id,
          status: participant.status || 'present',
          notes: participant.notes || null,
          created_at: new Date(participant.created_at),
          updated_at: participant.updated_at ? new Date(participant.updated_at) : new Date()
        }
      });
      successCount++;
    } catch (error) {
      console.error(`❌ Error migrating participant ${participant.id}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`✅ Migrated ${successCount} attendance participants (${errorCount} errors)`);
}

async function migrateAttendanceVisitors(visitors) {
  if (!visitors || visitors.length === 0) return;
  
  console.log('👋 Migrating attendance visitors...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const visitor of visitors) {
    try {
      await prisma.attendanceVisitor.create({
        data: {
          id: visitor.id,
          meeting_id: visitor.meeting_id,
          first_name: visitor.first_name || visitor.name?.split(' ')[0] || 'Unknown',
          last_name: visitor.last_name || visitor.name?.split(' ').slice(1).join(' ') || '',
          phone: visitor.phone || null,
          email: visitor.email || null,
          address: visitor.address || null,
          notes: visitor.notes || null,
          converted_to_member_id: visitor.converted_to_member_id || null,
          created_at: new Date(visitor.created_at)
        }
      });
      successCount++;
    } catch (error) {
      console.error(`❌ Error migrating visitor ${visitor.id}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`✅ Migrated ${successCount} attendance visitors (${errorCount} errors)`);
}

async function updateDistrictLeaders(districts) {
  if (!districts || districts.length === 0) return;
  
  console.log('🔄 Updating district leaders...');
  let successCount = 0;
  
  for (const district of districts) {
    if (district.leader1_id || district.leader2_id) {
      try {
        await prisma.district.update({
          where: { id: district.id },
          data: {
            leader1_id: district.leader1_id,
            leader2_id: district.leader2_id
          }
        });
        successCount++;
      } catch (error) {
        console.error(`❌ Error updating district ${district.id}: ${error.message}`);
      }
    }
  }
  
  console.log(`✅ Updated ${successCount} district leaders`);
}

async function main() {
  try {
    console.log('🚀 Starting final data migration...\n');
    
    const data = await pullData();
    console.log('');
    
    await clearNonDistrictData();
    console.log('');
    
    // Migrate in dependency order
    await migrateMembers(data.members);
    await updateDistrictLeaders(data.districts);
    await migrateCellGroups(data.cell_groups);
    await migrateCellGroupMembers(data.cell_group_members);
    await migrateCellGroupLeaders(data.cell_group_leaders);
    await migrateAttendanceMeetings(data.attendance_meetings);
    await migrateAttendanceParticipants(data.attendance_participants);
    await migrateAttendanceVisitors(data.attendance_visitors);
    
    console.log('\n🎉 Final migration completed!');
    console.log('\n📊 Run comparison to verify:');
    console.log('   node scripts/compare-databases.js');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

main();
