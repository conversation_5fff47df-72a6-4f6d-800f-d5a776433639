const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Read the Supabase dump file
const supabaseDumpPath = path.join(__dirname, '../../../Nextjs/churchManagement/supabase_full_dump.sql');

async function parseSupabaseData() {
  console.log('🔍 Parsing Supabase dump file...');
  
  const dumpContent = fs.readFileSync(supabaseDumpPath, 'utf8');
  
  // Extract table creation statements and data
  const tables = {};
  
  // Parse CREATE TABLE statements
  const createTableRegex = /CREATE TABLE[^;]+;/gs;
  const createMatches = dumpContent.match(createTableRegex) || [];
  
  console.log(`📋 Found ${createMatches.length} table definitions`);
  
  // For now, let's focus on the main tables we need
  const targetTables = [
    'members',
    'districts', 
    'cell_groups',
    'cell_group_members',
    'cell_group_leaders',
    'ministries',
    'ministry_members',
    'attendance_meetings',
    'attendance_participants', 
    'attendance_visitors',
    'classes',
    'class_levels',
    'class_sessions',
    'class_enrollments',
    'articles',
    'article_bookmarks',
    'article_comments',
    'member_tokens',
    'password_reset_tokens'
  ];
  
  return { targetTables, dumpContent };
}

async function createMySQLSchema() {
  console.log('🏗️  Creating MySQL schema...');
  
  try {
    // Push the Prisma schema to MySQL
    const { exec } = require('child_process');
    const util = require('util');
    const execPromise = util.promisify(exec);
    
    console.log('📤 Pushing Prisma schema to MySQL...');
    await execPromise('npx prisma db push --force-reset');
    console.log('✅ Schema created successfully');
    
  } catch (error) {
    console.error('❌ Error creating schema:', error.message);
    throw error;
  }
}

async function seedSampleData() {
  console.log('🌱 Seeding sample data...');
  
  try {
    // Create sample district
    const district = await prisma.district.create({
      data: {
        name: 'Central District',
        description: 'Main district in the city center',
        status: 'active'
      }
    });
    console.log('✅ Created sample district');

    // Create sample admin member
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    const adminMember = await prisma.member.create({
      data: {
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
        role: 'admin',
        role_level: 5,
        password_hash: hashedPassword,
        status: 'active',
        district_id: district.id
      }
    });
    console.log('✅ Created admin user');

    // Create sample cell group
    const cellGroup = await prisma.cellGroup.create({
      data: {
        name: 'Youth Cell Group',
        description: 'Cell group for young adults',
        district_id: district.id,
        leader_id: adminMember.id,
        meeting_day: 'friday',
        meeting_time: '19:00',
        meeting_location: 'Church Hall A',
        status: 'active'
      }
    });
    console.log('✅ Created sample cell group');

    // Create sample ministry
    const ministry = await prisma.ministry.create({
      data: {
        name: 'Worship Ministry',
        description: 'Leading worship services',
        leader_id: adminMember.id,
        status: 'active'
      }
    });
    console.log('✅ Created sample ministry');

    // Create sample class
    const classData = await prisma.class.create({
      data: {
        name: 'Bible Study 101',
        description: 'Introduction to Bible study',
        category: 'bible_study',
        max_students: 20,
        has_levels: true,
        status: 'active'
      }
    });
    console.log('✅ Created sample class');

    // Create sample article
    const article = await prisma.article.create({
      data: {
        title: 'Welcome to Our Church Management System',
        summary: 'Introduction to the new church management system',
        content: 'This is a comprehensive church management system built with modern technology...',
        category: 'announcement',
        status: 'published',
        featured: true,
        author_id: adminMember.id,
        view_count: 0
      }
    });
    console.log('✅ Created sample article');

    console.log('\n🎉 Sample data seeded successfully!');
    console.log('\n📋 Login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    
  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Supabase to MySQL migration...\n');
    
    // Step 1: Parse Supabase data
    const { targetTables } = await parseSupabaseData();
    
    // Step 2: Create MySQL schema
    await createMySQLSchema();
    
    // Step 3: Seed sample data
    await seedSampleData();
    
    console.log('\n✅ Migration completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Start the Node.js API server: npm run dev');
    console.log('2. Test the API endpoints');
    console.log('3. Update frontend to use the new API');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = { parseSupabaseData, createMySQLSchema, seedSampleData };
