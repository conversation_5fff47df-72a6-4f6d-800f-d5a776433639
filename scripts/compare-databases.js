const { createClient } = require('@supabase/supabase-js');
const { PrismaClient } = require('@prisma/client');

// Supabase configuration
const SUPABASE_URL = 'https://hjuogefczikouxbjmubj.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhqdW9nZWZjemlrb3V4YmptdWJqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDk4OTc3MiwiZXhwIjoyMDYwNTY1NzcyfQ.Vq7Ci3IlR7veGU134LkDHbXZadS6ghVQJTIGSGGhCuI';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const prisma = new PrismaClient();

async function getSupabaseData() {
  console.log('📊 Getting Supabase data...');
  
  const tables = ['districts', 'members', 'cell_groups', 'cell_group_members', 'cell_group_leaders', 'attendance_meetings', 'attendance_participants', 'attendance_visitors'];
  const data = {};
  
  for (const table of tables) {
    const { data: tableData, error } = await supabase.from(table).select('*');
    
    if (error) {
      console.log(`⚠️  ${table}: ${error.message}`);
      data[table] = [];
    } else {
      data[table] = tableData || [];
      console.log(`✅ Supabase ${table}: ${tableData?.length || 0} records`);
    }
  }
  
  return data;
}

async function getMySQLData() {
  console.log('🗄️  Getting MySQL data...');
  
  const data = {};
  
  try {
    data.districts = await prisma.district.findMany();
    console.log(`✅ MySQL districts: ${data.districts.length} records`);
    
    data.members = await prisma.member.findMany();
    console.log(`✅ MySQL members: ${data.members.length} records`);
    
    data.cell_groups = await prisma.cellGroup.findMany();
    console.log(`✅ MySQL cell_groups: ${data.cell_groups.length} records`);
    
    data.cell_group_members = await prisma.cellGroupMember.findMany();
    console.log(`✅ MySQL cell_group_members: ${data.cell_group_members.length} records`);
    
    data.cell_group_leaders = await prisma.cellGroupLeader.findMany();
    console.log(`✅ MySQL cell_group_leaders: ${data.cell_group_leaders.length} records`);
    
    data.attendance_meetings = await prisma.attendanceMeeting.findMany();
    console.log(`✅ MySQL attendance_meetings: ${data.attendance_meetings.length} records`);
    
    data.attendance_participants = await prisma.attendanceParticipant.findMany();
    console.log(`✅ MySQL attendance_participants: ${data.attendance_participants.length} records`);
    
    data.attendance_visitors = await prisma.attendanceVisitor.findMany();
    console.log(`✅ MySQL attendance_visitors: ${data.attendance_visitors.length} records`);
    
  } catch (error) {
    console.error('❌ Error getting MySQL data:', error.message);
  }
  
  return data;
}

function compareData(supabaseData, mysqlData) {
  console.log('\n🔍 Comparing data between Supabase and MySQL...\n');
  
  const tables = ['districts', 'members', 'cell_groups', 'cell_group_members', 'cell_group_leaders', 'attendance_meetings', 'attendance_participants', 'attendance_visitors'];
  
  let allMatch = true;
  
  for (const table of tables) {
    const supabaseCount = supabaseData[table]?.length || 0;
    const mysqlCount = mysqlData[table]?.length || 0;
    
    console.log(`📋 ${table}:`);
    console.log(`  Supabase: ${supabaseCount} records`);
    console.log(`  MySQL:    ${mysqlCount} records`);
    
    if (supabaseCount === mysqlCount) {
      console.log(`  ✅ Count matches`);
    } else {
      console.log(`  ❌ Count mismatch! Difference: ${Math.abs(supabaseCount - mysqlCount)}`);
      allMatch = false;
    }
    
    // Sample data comparison for first few records
    if (supabaseCount > 0 && mysqlCount > 0) {
      const sampleSize = Math.min(3, supabaseCount, mysqlCount);
      console.log(`  🔍 Checking first ${sampleSize} records...`);
      
      for (let i = 0; i < sampleSize; i++) {
        const supabaseRecord = supabaseData[table][i];
        const mysqlRecord = mysqlData[table][i];
        
        if (supabaseRecord && mysqlRecord) {
          if (supabaseRecord.id === mysqlRecord.id) {
            console.log(`    ✅ Record ${i + 1}: ID matches (${supabaseRecord.id})`);
          } else {
            console.log(`    ❌ Record ${i + 1}: ID mismatch`);
            console.log(`      Supabase: ${supabaseRecord.id}`);
            console.log(`      MySQL:    ${mysqlRecord.id}`);
            allMatch = false;
          }
        }
      }
    }
    
    console.log('');
  }
  
  return allMatch;
}

function generateSummaryReport(supabaseData, mysqlData) {
  console.log('📊 SUMMARY REPORT\n');
  console.log('=' .repeat(50));
  
  const tables = ['districts', 'members', 'cell_groups', 'cell_group_members', 'cell_group_leaders', 'attendance_meetings', 'attendance_participants', 'attendance_visitors'];
  
  let totalSupabase = 0;
  let totalMySQL = 0;
  
  console.log('Table                    | Supabase | MySQL   | Status');
  console.log('-'.repeat(50));
  
  for (const table of tables) {
    const supabaseCount = supabaseData[table]?.length || 0;
    const mysqlCount = mysqlData[table]?.length || 0;
    
    totalSupabase += supabaseCount;
    totalMySQL += mysqlCount;
    
    const status = supabaseCount === mysqlCount ? '✅ Match' : '❌ Diff';
    const tableName = table.padEnd(24);
    const sbCount = supabaseCount.toString().padStart(8);
    const myCount = mysqlCount.toString().padStart(7);
    
    console.log(`${tableName} | ${sbCount} | ${myCount} | ${status}`);
  }
  
  console.log('-'.repeat(50));
  console.log(`${'TOTAL'.padEnd(24)} | ${totalSupabase.toString().padStart(8)} | ${totalMySQL.toString().padStart(7)} | ${totalSupabase === totalMySQL ? '✅ Match' : '❌ Diff'}`);
  
  console.log('\n📈 Migration Status:');
  if (totalSupabase === totalMySQL && totalMySQL > 0) {
    console.log('🎉 SUCCESS: All data has been successfully migrated!');
    console.log(`📊 Total records migrated: ${totalMySQL}`);
  } else if (totalMySQL === 0) {
    console.log('⚠️  WARNING: No data found in MySQL. Migration may not have run yet.');
  } else {
    console.log('❌ INCOMPLETE: Some data is missing or migration is incomplete.');
    console.log(`📊 Expected: ${totalSupabase} records`);
    console.log(`📊 Found:    ${totalMySQL} records`);
    console.log(`📊 Missing:  ${totalSupabase - totalMySQL} records`);
  }
}

async function main() {
  try {
    console.log('🔍 Database Comparison Tool\n');
    console.log('=' .repeat(50));
    
    // Get data from both databases
    const supabaseData = await getSupabaseData();
    console.log('');
    const mysqlData = await getMySQLData();
    
    // Compare data
    const allMatch = compareData(supabaseData, mysqlData);
    
    // Generate summary report
    generateSummaryReport(supabaseData, mysqlData);
    
    console.log('\n🔧 Next Steps:');
    if (allMatch && Object.values(mysqlData).some(table => table.length > 0)) {
      console.log('✅ Data migration is complete and verified!');
      console.log('🚀 You can now use the MySQL database for your API.');
    } else if (Object.values(mysqlData).every(table => table.length === 0)) {
      console.log('📝 Run the migration script to transfer data:');
      console.log('   npm run pull:supabase');
    } else {
      console.log('🔄 Some data may be missing. Consider re-running migration:');
      console.log('   npm run pull:supabase');
    }
    
  } catch (error) {
    console.error('\n❌ Comparison failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

main();
