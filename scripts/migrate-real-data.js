const { createClient } = require('@supabase/supabase-js');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Supabase configuration
const SUPABASE_URL = 'https://hjuogefczikouxbjmubj.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhqdW9nZWZjemlrb3V4YmptdWJqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDk4OTc3MiwiZXhwIjoyMDYwNTY1NzcyfQ.Vq7Ci3IlR7veGU134LkDHbXZadS6ghVQJTIGSGGhCuI';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const prisma = new PrismaClient();

async function pullData() {
  console.log('🔍 Pulling data from Supabase...');
  
  const tables = ['districts', 'members', 'cell_groups', 'cell_group_members', 'cell_group_leaders', 'attendance_meetings', 'attendance_participants', 'attendance_visitors'];
  const data = {};
  
  for (const table of tables) {
    console.log(`📊 Pulling ${table}...`);
    const { data: tableData, error } = await supabase.from(table).select('*');
    
    if (error) {
      console.log(`⚠️  ${table}: ${error.message}`);
      data[table] = [];
    } else {
      data[table] = tableData || [];
      console.log(`✅ ${table}: ${tableData?.length || 0} records`);
    }
  }
  
  return data;
}

async function clearData() {
  console.log('🗑️  Clearing existing data...');
  
  await prisma.attendanceVisitor.deleteMany();
  await prisma.attendanceParticipant.deleteMany();
  await prisma.attendanceMeeting.deleteMany();
  await prisma.cellGroupLeader.deleteMany();
  await prisma.cellGroupMember.deleteMany();
  await prisma.cellGroup.deleteMany();
  await prisma.memberToken.deleteMany();
  await prisma.passwordResetToken.deleteMany();
  await prisma.member.deleteMany();
  await prisma.district.deleteMany();
  
  console.log('✅ Data cleared');
}

async function migrateData(data) {
  console.log('🔄 Starting migration...');
  
  // 1. Districts (without leaders)
  if (data.districts?.length > 0) {
    console.log('📍 Migrating districts...');
    for (const district of data.districts) {
      await prisma.district.create({
        data: {
          id: district.id,
          name: district.name,
          description: district.description || null,
          status: district.status || 'active',
          created_at: new Date(district.created_at),
          updated_at: new Date(district.updated_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.districts.length} districts`);
  }
  
  // 2. Members
  if (data.members?.length > 0) {
    console.log('👥 Migrating members...');
    for (const member of data.members) {
      let passwordHash = null;
      if (member.password_hash) {
        passwordHash = member.password_hash;
      } else {
        passwordHash = await bcrypt.hash('password123', 12);
      }
      
      await prisma.member.create({
        data: {
          id: member.id,
          email: member.email,
          first_name: member.first_name,
          last_name: member.last_name,
          phone: member.phone || null,
          address: member.address || null,
          date_of_birth: member.date_of_birth ? new Date(member.date_of_birth) : null,
          gender: member.gender || null,
          marital_status: member.marital_status || null,
          occupation: member.occupation || null,
          emergency_contact_name: member.emergency_contact_name || null,
          emergency_contact_phone: member.emergency_contact_phone || null,
          status: member.status || 'active',
          role: member.role || 'member',
          role_level: member.role_level || 1,
          join_date: member.join_date ? new Date(member.join_date) : new Date(),
          district_id: member.district_id || null,
          cell_group_id: member.cell_group_id || null,
          password_hash: passwordHash,
          created_at: new Date(member.created_at),
          updated_at: new Date(member.updated_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.members.length} members`);
  }
  
  // 3. Update district leaders
  if (data.districts?.length > 0) {
    console.log('🔄 Updating district leaders...');
    for (const district of data.districts) {
      if (district.leader1_id || district.leader2_id) {
        await prisma.district.update({
          where: { id: district.id },
          data: {
            leader1_id: district.leader1_id,
            leader2_id: district.leader2_id
          }
        });
      }
    }
    console.log('✅ Updated district leaders');
  }
  
  // 4. Cell Groups
  if (data.cell_groups?.length > 0) {
    console.log('🏠 Migrating cell groups...');
    for (const cellGroup of data.cell_groups) {
      await prisma.cellGroup.create({
        data: {
          id: cellGroup.id,
          name: cellGroup.name,
          description: cellGroup.description || null,
          district_id: cellGroup.district_id,
          leader_id: cellGroup.leader_id,
          assistant_leader_id: cellGroup.assistant_leader_id,
          meeting_day: cellGroup.meeting_day?.toLowerCase() || null,
          meeting_time: cellGroup.meeting_time || null,
          meeting_location: cellGroup.meeting_location || null,
          status: cellGroup.status || 'active',
          created_at: new Date(cellGroup.created_at),
          updated_at: new Date(cellGroup.updated_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.cell_groups.length} cell groups`);
  }
  
  // 5. Cell Group Members
  if (data.cell_group_members?.length > 0) {
    console.log('👥 Migrating cell group members...');
    for (const cgMember of data.cell_group_members) {
      await prisma.cellGroupMember.create({
        data: {
          id: cgMember.id,
          cell_group_id: cgMember.cell_group_id,
          member_id: cgMember.member_id,
          role: cgMember.role || 'member',
          joined_at: cgMember.joined_at ? new Date(cgMember.joined_at) : new Date(),
          created_at: new Date(cgMember.created_at),
          updated_at: new Date(cgMember.updated_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.cell_group_members.length} cell group members`);
  }
  
  // 6. Cell Group Leaders
  if (data.cell_group_leaders?.length > 0) {
    console.log('👑 Migrating cell group leaders...');
    for (const cgLeader of data.cell_group_leaders) {
      await prisma.cellGroupLeader.create({
        data: {
          id: cgLeader.id,
          cell_group_id: cgLeader.cell_group_id,
          member_id: cgLeader.member_id,
          role: cgLeader.role || 'leader',
          created_at: new Date(cgLeader.created_at),
          updated_at: new Date(cgLeader.updated_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.cell_group_leaders.length} cell group leaders`);
  }
  
  // 7. Attendance Meetings
  if (data.attendance_meetings?.length > 0) {
    console.log('📅 Migrating attendance meetings...');
    for (const meeting of data.attendance_meetings) {
      await prisma.attendanceMeeting.create({
        data: {
          id: meeting.id,
          cell_group_id: meeting.cell_group_id,
          meeting_date: new Date(meeting.meeting_date),
          meeting_type: meeting.meeting_type || 'regular',
          topic: meeting.topic || null,
          notes: meeting.notes || null,
          location: meeting.location || null,
          offering: meeting.offering ? parseFloat(meeting.offering) : null,
          created_by: meeting.created_by || null,
          ministry_id: meeting.ministry_id || null,
          event_category: meeting.event_category || null,
          is_realtime: meeting.is_realtime || false,
          created_at: new Date(meeting.created_at),
          updated_at: new Date(meeting.updated_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.attendance_meetings.length} attendance meetings`);
  }
  
  // 8. Attendance Participants
  if (data.attendance_participants?.length > 0) {
    console.log('✋ Migrating attendance participants...');
    for (const participant of data.attendance_participants) {
      await prisma.attendanceParticipant.create({
        data: {
          id: participant.id,
          meeting_id: participant.meeting_id,
          member_id: participant.member_id,
          status: participant.status || 'present',
          notes: participant.notes || null,
          created_at: new Date(participant.created_at),
          updated_at: new Date(participant.updated_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.attendance_participants.length} attendance participants`);
  }
  
  // 9. Attendance Visitors
  if (data.attendance_visitors?.length > 0) {
    console.log('👋 Migrating attendance visitors...');
    for (const visitor of data.attendance_visitors) {
      await prisma.attendanceVisitor.create({
        data: {
          id: visitor.id,
          meeting_id: visitor.meeting_id,
          first_name: visitor.first_name || visitor.name?.split(' ')[0] || 'Unknown',
          last_name: visitor.last_name || visitor.name?.split(' ').slice(1).join(' ') || '',
          phone: visitor.phone || null,
          email: visitor.email || null,
          address: visitor.address || null,
          notes: visitor.notes || null,
          converted_to_member_id: visitor.converted_to_member_id || null,
          created_at: new Date(visitor.created_at)
        }
      });
    }
    console.log(`✅ Migrated ${data.attendance_visitors.length} attendance visitors`);
  }
  
  console.log('✅ Migration completed successfully!');
}

async function main() {
  try {
    console.log('🚀 Starting real data migration...\n');
    
    const data = await pullData();
    await clearData();
    await migrateData(data);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Summary:');
    Object.entries(data).forEach(([table, records]) => {
      if (records.length > 0) {
        console.log(`  ✅ ${table}: ${records.length} records migrated`);
      }
    });
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

main();
