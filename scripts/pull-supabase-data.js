const { createClient } = require('@supabase/supabase-js');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Supabase configuration
const SUPABASE_URL = 'https://hjuogefczikouxbjmubj.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhqdW9nZWZjemlrb3V4YmptdWJqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDk4OTc3MiwiZXhwIjoyMDYwNTY1NzcyfQ.Vq7Ci3IlR7veGU134LkDHbXZadS6ghVQJTIGSGGhCuI';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const prisma = new PrismaClient();

async function pullSupabaseData() {
  console.log('🔍 Connecting to Supabase...');
  
  try {
    // Test connection
    const { data: testData, error: testError } = await supabase
      .from('members')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('❌ Failed to connect to Supabase:', testError.message);
      return null;
    }
    
    console.log('✅ Connected to Supabase successfully');
    
    // Pull data from each table
    const tables = [
      'districts',
      'members', 
      'cell_groups',
      'cell_group_members',
      'cell_group_leaders',
      'ministries',
      'ministry_members',
      'attendance_meetings',
      'attendance_participants',
      'attendance_visitors',
      'classes',
      'class_levels',
      'class_sessions',
      'class_enrollments',
      'articles',
      'article_bookmarks',
      'article_comments'
    ];
    
    const supabaseData = {};
    
    for (const table of tables) {
      console.log(`📊 Pulling data from ${table}...`);
      
      const { data, error } = await supabase
        .from(table)
        .select('*');
      
      if (error) {
        console.log(`⚠️  Table ${table} not found or error: ${error.message}`);
        supabaseData[table] = [];
      } else {
        supabaseData[table] = data || [];
        console.log(`✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    return supabaseData;
    
  } catch (error) {
    console.error('❌ Error pulling Supabase data:', error.message);
    return null;
  }
}

async function clearExistingData() {
  console.log('🗑️  Clearing existing data...');
  
  try {
    // Delete in reverse order to respect foreign key constraints
    await prisma.articleComment.deleteMany();
    await prisma.articleBookmark.deleteMany();
    await prisma.article.deleteMany();
    await prisma.classEnrollment.deleteMany();
    await prisma.classSession.deleteMany();
    await prisma.classLevel.deleteMany();
    await prisma.class.deleteMany();
    await prisma.attendanceVisitor.deleteMany();
    await prisma.attendanceParticipant.deleteMany();
    await prisma.attendanceMeeting.deleteMany();
    await prisma.ministryMember.deleteMany();
    await prisma.ministry.deleteMany();
    await prisma.cellGroupLeader.deleteMany();
    await prisma.cellGroupMember.deleteMany();
    await prisma.cellGroup.deleteMany();
    await prisma.memberToken.deleteMany();
    await prisma.passwordResetToken.deleteMany();
    await prisma.member.deleteMany();
    await prisma.district.deleteMany();
    
    console.log('✅ Existing data cleared');
  } catch (error) {
    console.error('❌ Error clearing data:', error.message);
    throw error;
  }
}

async function migrateData(supabaseData) {
  console.log('🔄 Starting data migration...');
  
  try {
    // 1. Migrate Districts
    if (supabaseData.districts?.length > 0) {
      console.log('📍 Migrating districts...');
      for (const district of supabaseData.districts) {
        await prisma.district.create({
          data: {
            id: district.id,
            name: district.name,
            description: district.description,
            status: district.status || 'active',
            leader1_id: district.leader1_id,
            leader2_id: district.leader2_id,
            created_at: district.created_at ? new Date(district.created_at) : new Date(),
            updated_at: district.updated_at ? new Date(district.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.districts.length} districts`);
    }
    
    // 2. Migrate Members
    if (supabaseData.members?.length > 0) {
      console.log('👥 Migrating members...');
      for (const member of supabaseData.members) {
        // Hash password if exists, otherwise use default
        let passwordHash = null;
        if (member.password_hash) {
          passwordHash = member.password_hash;
        } else if (member.password) {
          passwordHash = await bcrypt.hash(member.password, 12);
        } else {
          passwordHash = await bcrypt.hash('password123', 12); // Default password
        }
        
        await prisma.member.create({
          data: {
            id: member.id,
            email: member.email,
            first_name: member.first_name,
            last_name: member.last_name,
            phone: member.phone,
            address: member.address,
            birth_date: member.birth_date ? new Date(member.birth_date) : null,
            gender: member.gender,
            marital_status: member.marital_status,
            occupation: member.occupation,
            emergency_contact_name: member.emergency_contact_name,
            emergency_contact_phone: member.emergency_contact_phone,
            status: member.status || 'active',
            role: member.role || 'member',
            role_level: member.role_level || 1,
            join_date: member.join_date ? new Date(member.join_date) : new Date(),
            district_id: member.district_id,
            cell_group_id: member.cell_group_id,
            password_hash: passwordHash,
            created_at: member.created_at ? new Date(member.created_at) : new Date(),
            updated_at: member.updated_at ? new Date(member.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.members.length} members`);
    }
    
    // 3. Migrate Cell Groups
    if (supabaseData.cell_groups?.length > 0) {
      console.log('🏠 Migrating cell groups...');
      for (const cellGroup of supabaseData.cell_groups) {
        await prisma.cellGroup.create({
          data: {
            id: cellGroup.id,
            name: cellGroup.name,
            description: cellGroup.description,
            district_id: cellGroup.district_id,
            leader_id: cellGroup.leader_id,
            assistant_leader_id: cellGroup.assistant_leader_id,
            meeting_day: cellGroup.meeting_day,
            meeting_time: cellGroup.meeting_time,
            meeting_location: cellGroup.meeting_location,
            status: cellGroup.status || 'active',
            created_at: cellGroup.created_at ? new Date(cellGroup.created_at) : new Date(),
            updated_at: cellGroup.updated_at ? new Date(cellGroup.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.cell_groups.length} cell groups`);
    }
    
    // Continue with other tables...
    console.log('✅ Data migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during migration:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Supabase data pull and migration...\n');
    
    // Step 1: Pull data from Supabase
    const supabaseData = await pullSupabaseData();
    if (!supabaseData) {
      console.error('❌ Failed to pull data from Supabase');
      return;
    }
    
    // Step 2: Clear existing data
    await clearExistingData();
    
    // Step 3: Migrate data
    await migrateData(supabaseData);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Summary:');
    Object.entries(supabaseData).forEach(([table, data]) => {
      if (data.length > 0) {
        console.log(`  ✅ ${table}: ${data.length} records migrated`);
      }
    });
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = { pullSupabaseData, migrateData };
