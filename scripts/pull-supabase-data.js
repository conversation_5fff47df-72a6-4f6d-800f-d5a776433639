const { createClient } = require('@supabase/supabase-js');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Supabase configuration
const SUPABASE_URL = 'https://hjuogefczikouxbjmubj.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhqdW9nZWZjemlrb3V4YmptdWJqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDk4OTc3MiwiZXhwIjoyMDYwNTY1NzcyfQ.Vq7Ci3IlR7veGU134LkDHbXZadS6ghVQJTIGSGGhCuI';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const prisma = new PrismaClient();

async function pullSupabaseData() {
  console.log('🔍 Connecting to Supabase...');
  
  try {
    // Test connection
    const { data: testData, error: testError } = await supabase
      .from('members')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('❌ Failed to connect to Supabase:', testError.message);
      return null;
    }
    
    console.log('✅ Connected to Supabase successfully');
    
    // Pull data from each table
    const tables = [
      'districts',
      'members', 
      'cell_groups',
      'cell_group_members',
      'cell_group_leaders',
      'ministries',
      'ministry_members',
      'attendance_meetings',
      'attendance_participants',
      'attendance_visitors',
      'classes',
      'class_levels',
      'class_sessions',
      'class_enrollments',
      'articles',
      'article_bookmarks',
      'article_comments'
    ];
    
    const supabaseData = {};
    
    for (const table of tables) {
      console.log(`📊 Pulling data from ${table}...`);
      
      const { data, error } = await supabase
        .from(table)
        .select('*');
      
      if (error) {
        console.log(`⚠️  Table ${table} not found or error: ${error.message}`);
        supabaseData[table] = [];
      } else {
        supabaseData[table] = data || [];
        console.log(`✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    return supabaseData;
    
  } catch (error) {
    console.error('❌ Error pulling Supabase data:', error.message);
    return null;
  }
}

async function clearExistingData() {
  console.log('🗑️  Clearing existing data...');
  
  try {
    // Delete in reverse order to respect foreign key constraints
    await prisma.articleComment.deleteMany();
    await prisma.articleBookmark.deleteMany();
    await prisma.article.deleteMany();
    await prisma.classEnrollment.deleteMany();
    await prisma.classSession.deleteMany();
    await prisma.classLevel.deleteMany();
    await prisma.class.deleteMany();
    await prisma.attendanceVisitor.deleteMany();
    await prisma.attendanceParticipant.deleteMany();
    await prisma.attendanceMeeting.deleteMany();
    await prisma.ministryMember.deleteMany();
    await prisma.ministry.deleteMany();
    await prisma.cellGroupLeader.deleteMany();
    await prisma.cellGroupMember.deleteMany();
    await prisma.cellGroup.deleteMany();
    await prisma.memberToken.deleteMany();
    await prisma.passwordResetToken.deleteMany();
    await prisma.member.deleteMany();
    await prisma.district.deleteMany();
    
    console.log('✅ Existing data cleared');
  } catch (error) {
    console.error('❌ Error clearing data:', error.message);
    throw error;
  }
}

async function migrateData(supabaseData) {
  console.log('🔄 Starting data migration...');

  try {
    // 1. Migrate Districts (without leaders first)
    if (supabaseData.districts?.length > 0) {
      console.log('📍 Migrating districts...');
      for (const district of supabaseData.districts) {
        await prisma.district.create({
          data: {
            id: district.id,
            name: district.name,
            description: district.description || null,
            status: district.status || 'active',
            // Skip leaders for now, will update later
            created_at: district.created_at ? new Date(district.created_at) : new Date(),
            updated_at: district.updated_at ? new Date(district.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.districts.length} districts`);
    }
    
    // 2. Migrate Members
    if (supabaseData.members?.length > 0) {
      console.log('👥 Migrating members...');
      for (const member of supabaseData.members) {
        // Hash password if exists, otherwise use default
        let passwordHash = null;
        if (member.password_hash) {
          passwordHash = member.password_hash;
        } else if (member.password) {
          passwordHash = await bcrypt.hash(member.password, 12);
        } else {
          passwordHash = await bcrypt.hash('password123', 12); // Default password
        }
        
        await prisma.member.create({
          data: {
            id: member.id,
            email: member.email,
            first_name: member.first_name,
            last_name: member.last_name,
            phone: member.phone || null,
            address: member.address || null,
            birth_date: member.date_of_birth ? new Date(member.date_of_birth) : null,
            gender: member.gender || null,
            marital_status: member.marital_status || null,
            occupation: member.occupation || null,
            emergency_contact_name: member.emergency_contact_name || null,
            emergency_contact_phone: member.emergency_contact_phone || null,
            status: member.status || 'active',
            role: member.role || 'member',
            role_level: member.role_level || 1,
            join_date: member.join_date ? new Date(member.join_date) : new Date(),
            district_id: member.district_id || null,
            cell_group_id: member.cell_group_id || null,
            password_hash: passwordHash,
            created_at: member.created_at ? new Date(member.created_at) : new Date(),
            updated_at: member.updated_at ? new Date(member.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.members.length} members`);
    }
    
    // 3. Migrate Cell Groups
    if (supabaseData.cell_groups?.length > 0) {
      console.log('🏠 Migrating cell groups...');
      for (const cellGroup of supabaseData.cell_groups) {
        await prisma.cellGroup.create({
          data: {
            id: cellGroup.id,
            name: cellGroup.name,
            description: cellGroup.description,
            district_id: cellGroup.district_id,
            leader_id: cellGroup.leader_id,
            assistant_leader_id: cellGroup.assistant_leader_id,
            meeting_day: cellGroup.meeting_day,
            meeting_time: cellGroup.meeting_time,
            meeting_location: cellGroup.meeting_location,
            status: cellGroup.status || 'active',
            created_at: cellGroup.created_at ? new Date(cellGroup.created_at) : new Date(),
            updated_at: cellGroup.updated_at ? new Date(cellGroup.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.cell_groups.length} cell groups`);
    }
    
    // 4. Update District Leaders (now that members exist)
    if (supabaseData.districts?.length > 0) {
      console.log('🔄 Updating district leaders...');
      for (const district of supabaseData.districts) {
        if (district.leader1_id || district.leader2_id) {
          await prisma.district.update({
            where: { id: district.id },
            data: {
              leader1_id: district.leader1_id,
              leader2_id: district.leader2_id
            }
          });
        }
      }
      console.log('✅ Updated district leaders');
    }

    // 5. Migrate Cell Group Members
    if (supabaseData.cell_group_members?.length > 0) {
      console.log('👥 Migrating cell group members...');
      for (const cgMember of supabaseData.cell_group_members) {
        await prisma.cellGroupMember.create({
          data: {
            id: cgMember.id,
            cell_group_id: cgMember.cell_group_id,
            member_id: cgMember.member_id,
            role: cgMember.role || 'member',
            joined_at: cgMember.joined_at ? new Date(cgMember.joined_at) : new Date(),
            created_at: cgMember.created_at ? new Date(cgMember.created_at) : new Date(),
            updated_at: cgMember.updated_at ? new Date(cgMember.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.cell_group_members.length} cell group members`);
    }

    // 6. Migrate Cell Group Leaders
    if (supabaseData.cell_group_leaders?.length > 0) {
      console.log('👑 Migrating cell group leaders...');
      for (const cgLeader of supabaseData.cell_group_leaders) {
        await prisma.cellGroupLeader.create({
          data: {
            id: cgLeader.id,
            cell_group_id: cgLeader.cell_group_id,
            member_id: cgLeader.member_id,
            role: cgLeader.role || 'leader',
            created_at: cgLeader.created_at ? new Date(cgLeader.created_at) : new Date(),
            updated_at: cgLeader.updated_at ? new Date(cgLeader.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.cell_group_leaders.length} cell group leaders`);
    }

    // 7. Migrate Attendance Meetings
    if (supabaseData.attendance_meetings?.length > 0) {
      console.log('📅 Migrating attendance meetings...');
      for (const meeting of supabaseData.attendance_meetings) {
        await prisma.attendanceMeeting.create({
          data: {
            id: meeting.id,
            cell_group_id: meeting.cell_group_id,
            meeting_date: meeting.meeting_date ? new Date(meeting.meeting_date) : new Date(),
            meeting_type: meeting.meeting_type || 'regular',
            topic: meeting.topic,
            notes: meeting.notes,
            location: meeting.location,
            offering: meeting.offering ? parseFloat(meeting.offering) : null,
            created_by: meeting.created_by,
            ministry_id: meeting.ministry_id,
            event_category: meeting.event_category,
            is_realtime: meeting.is_realtime || false,
            created_at: meeting.created_at ? new Date(meeting.created_at) : new Date(),
            updated_at: meeting.updated_at ? new Date(meeting.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.attendance_meetings.length} attendance meetings`);
    }

    // 8. Migrate Attendance Participants
    if (supabaseData.attendance_participants?.length > 0) {
      console.log('✋ Migrating attendance participants...');
      for (const participant of supabaseData.attendance_participants) {
        await prisma.attendanceParticipant.create({
          data: {
            id: participant.id,
            meeting_id: participant.meeting_id,
            member_id: participant.member_id,
            status: participant.status || 'present',
            notes: participant.notes,
            created_at: participant.created_at ? new Date(participant.created_at) : new Date(),
            updated_at: participant.updated_at ? new Date(participant.updated_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.attendance_participants.length} attendance participants`);
    }

    // 9. Migrate Attendance Visitors
    if (supabaseData.attendance_visitors?.length > 0) {
      console.log('👋 Migrating attendance visitors...');
      for (const visitor of supabaseData.attendance_visitors) {
        await prisma.attendanceVisitor.create({
          data: {
            id: visitor.id,
            meeting_id: visitor.meeting_id,
            first_name: visitor.first_name || visitor.name?.split(' ')[0] || 'Unknown',
            last_name: visitor.last_name || visitor.name?.split(' ').slice(1).join(' ') || '',
            phone: visitor.phone,
            email: visitor.email,
            address: visitor.address,
            notes: visitor.notes,
            converted_to_member_id: visitor.converted_to_member_id,
            created_at: visitor.created_at ? new Date(visitor.created_at) : new Date()
          }
        });
      }
      console.log(`✅ Migrated ${supabaseData.attendance_visitors.length} attendance visitors`);
    }

    console.log('✅ Data migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during migration:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Supabase data pull and migration...\n');
    
    // Step 1: Pull data from Supabase
    const supabaseData = await pullSupabaseData();
    if (!supabaseData) {
      console.error('❌ Failed to pull data from Supabase');
      return;
    }
    
    // Step 2: Clear existing data
    await clearExistingData();
    
    // Step 3: Migrate data
    await migrateData(supabaseData);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Summary:');
    Object.entries(supabaseData).forEach(([table, data]) => {
      if (data.length > 0) {
        console.log(`  ✅ ${table}: ${data.length} records migrated`);
      }
    });
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = { pullSupabaseData, migrateData };
