const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://hjuogefczikouxbjmubj.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhqdW9nZWZjemlrb3V4YmptdWJqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDk4OTc3MiwiZXhwIjoyMDYwNTY1NzcyfQ.Vq7Ci3IlR7veGU134LkDHbXZadS6ghVQJTIGSGGhCuI';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function debugData() {
  console.log('🔍 Debugging Supabase data...');
  
  try {
    // Check districts data
    const { data: districts, error: districtError } = await supabase
      .from('districts')
      .select('*')
      .limit(3);
    
    if (districtError) {
      console.error('❌ Error fetching districts:', districtError.message);
      return;
    }
    
    console.log('\n📊 Sample Districts Data:');
    console.log(JSON.stringify(districts, null, 2));
    
    // Check members data
    const { data: members, error: memberError } = await supabase
      .from('members')
      .select('*')
      .limit(3);
    
    if (memberError) {
      console.error('❌ Error fetching members:', memberError.message);
      return;
    }
    
    console.log('\n👥 Sample Members Data:');
    console.log(JSON.stringify(members, null, 2));
    
    // Check cell groups data
    const { data: cellGroups, error: cgError } = await supabase
      .from('cell_groups')
      .select('*')
      .limit(3);
    
    if (cgError) {
      console.error('❌ Error fetching cell groups:', cgError.message);
      return;
    }
    
    console.log('\n🏠 Sample Cell Groups Data:');
    console.log(JSON.stringify(cellGroups, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugData();
