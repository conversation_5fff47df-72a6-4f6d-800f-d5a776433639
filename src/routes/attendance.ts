import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { AttendanceController } from '../controllers/AttendanceController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();
const attendanceController = new AttendanceController();

// Get all meetings (with pagination and filters)
router.get('/meetings', authenticateToken, attendanceController.getMeetings);

// Get meeting by ID
router.get('/meetings/:id', [
  authenticateToken,
  param('id').isUUID(),
], attendanceController.getMeetingById);

// Create new meeting with attendance
router.post('/meetings', [
  authenticateToken,
  requireAdmin,
  body('event_category').isIn(['cell_group', 'ministry', 'service', 'prayer', 'class', 'other']),
  body('meeting_date').isISO8601(),
  body('meeting_type').isLength({ min: 1 }).trim(),
  body('topic').optional().trim(),
  body('notes').optional().trim(),
  body('location').optional().trim(),
  body('offering').optional().isFloat({ min: 0 }),
  body('cell_group_id').optional().isUUID(),
  body('ministry_id').optional().isUUID(),
  body('participants').isArray(),
  body('participants.*.member_id').isUUID(),
  body('participants.*.status').isIn(['present', 'absent', 'late']),
  body('visitors').optional().isArray(),
  body('visitors.*.name').isLength({ min: 1 }).trim(),
], attendanceController.createMeeting);

// Update meeting
router.put('/meetings/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('meeting_date').optional().isISO8601(),
  body('meeting_type').optional().isLength({ min: 1 }).trim(),
  body('topic').optional().trim(),
  body('notes').optional().trim(),
  body('location').optional().trim(),
  body('offering').optional().isFloat({ min: 0 }),
], attendanceController.updateMeeting);

// Delete meeting
router.delete('/meetings/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], attendanceController.deleteMeeting);

// Get meeting participants
router.get('/meetings/:id/participants', [
  authenticateToken,
  param('id').isUUID(),
], attendanceController.getMeetingParticipants);

// Update meeting participants
router.put('/meetings/:id/participants', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('participants').isArray(),
  body('participants.*.member_id').isUUID(),
  body('participants.*.status').isIn(['present', 'absent', 'late']),
], attendanceController.updateMeetingParticipants);

// Get meeting visitors
router.get('/meetings/:id/visitors', [
  authenticateToken,
  param('id').isUUID(),
], attendanceController.getMeetingVisitors);

// Add visitors to meeting
router.post('/meetings/:id/visitors', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('visitors').isArray(),
  body('visitors.*.name').isLength({ min: 1 }).trim(),
], attendanceController.addMeetingVisitors);

// Get attendance statistics
router.get('/stats', [
  authenticateToken,
  query('start_date').optional().isISO8601(),
  query('end_date').optional().isISO8601(),
  query('event_category').optional().isIn(['cell_group', 'ministry', 'service', 'prayer', 'class', 'other']),
], attendanceController.getAttendanceStats);

// Get member attendance history
router.get('/members/:memberId/history', [
  authenticateToken,
  param('memberId').isUUID(),
], attendanceController.getMemberAttendanceHistory);

// Enable/disable real-time for meeting
router.patch('/meetings/:id/realtime', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('is_realtime').isBoolean(),
], attendanceController.toggleMeetingRealtime);

export default router;
