import { Router } from 'express';
import { body } from 'express-validator';
import { AuthController } from '../controllers/AuthController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const authController = new AuthController();

// Admin login
router.post('/admin/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
], authController.adminLogin);

// Member login
router.post('/member/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
], authController.memberLogin);

// Check member exists
router.post('/member/check', [
  body('email').isEmail().normalizeEmail(),
], authController.checkMember);

// Reset password
router.post('/member/reset-password', [
  body('memberId').isUUID(),
  body('currentPassword').isLength({ min: 1 }),
  body('newPassword').isLength({ min: 8 }),
], authController.resetPassword);

// Refresh token
router.post('/refresh', authController.refreshToken);

// Logout
router.post('/logout', authenticateToken, authController.logout);

// Get current user
router.get('/me', authenticateToken, authController.getCurrentUser);

export default router;
