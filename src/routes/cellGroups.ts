import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { CellGroupController } from '../controllers/CellGroupController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();
const cellGroupController = new CellGroupController();

// Get all cell groups (with pagination and search)
router.get('/', authenticateToken, cellGroupController.getCellGroups);

// Get cell group by ID
router.get('/:id', [
  authenticateToken,
  param('id').isUUID(),
], cellGroupController.getCellGroupById);

// Create new cell group
router.post('/', [
  authenticateToken,
  requireAdmin,
  body('name').isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('district_id').optional().isUUID(),
  body('leader_id').optional().isUUID(),
  body('assistant_leader_id').optional().isUUID(),
  body('meeting_day').optional().isIn(['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']),
  body('meeting_time').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
], cellGroupController.createCellGroup);

// Update cell group
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('name').optional().isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('district_id').optional().isUUID(),
  body('leader_id').optional().isUUID(),
  body('assistant_leader_id').optional().isUUID(),
  body('meeting_day').optional().isIn(['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']),
  body('meeting_time').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
], cellGroupController.updateCellGroup);

// Delete cell group
router.delete('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], cellGroupController.deleteCellGroup);

// Get cell group members
router.get('/:id/members', [
  authenticateToken,
  param('id').isUUID(),
], cellGroupController.getCellGroupMembers);

// Add members to cell group
router.post('/:id/members', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('member_ids').isArray(),
  body('member_ids.*').isUUID(),
], cellGroupController.addMembersToCellGroup);

// Remove member from cell group
router.delete('/:id/members/:memberId', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  param('memberId').isUUID(),
], cellGroupController.removeMemberFromCellGroup);

// Get cell group meetings/attendance
router.get('/:id/meetings', [
  authenticateToken,
  param('id').isUUID(),
], cellGroupController.getCellGroupMeetings);

export default router;
