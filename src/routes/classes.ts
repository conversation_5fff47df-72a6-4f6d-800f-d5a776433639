import { Router } from 'express';
import { body, param } from 'express-validator';
import { ClassController } from '../controllers/ClassController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();
const classController = new ClassController();

// Get all classes
router.get('/', authenticateToken, classController.getClasses);

// Get class by ID
router.get('/:id', [
  authenticateToken,
  param('id').isUUID(),
], classController.getClassById);

// Create new class
router.post('/', [
  authenticateToken,
  requireAdmin,
  body('name').isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('category').isIn(['bible_study', 'counseling', 'discipleship', 'leadership', 'other']),
  body('max_students').optional().isInt({ min: 1 }),
  body('has_levels').isBoolean(),
], classController.createClass);

// Update class
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], classController.updateClass);

// Delete class
router.delete('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], classController.deleteClass);

// Get class levels
router.get('/:id/levels', [
  authenticateToken,
  param('id').isUUID(),
], classController.getClassLevels);

// Create class level
router.post('/:id/levels', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('name').isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('order_number').isInt({ min: 1 }),
], classController.createClassLevel);

// Get class sessions
router.get('/:id/sessions', [
  authenticateToken,
  param('id').isUUID(),
], classController.getClassSessions);

// Create class session
router.post('/:id/sessions', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('title').isLength({ min: 1 }).trim(),
  body('session_date').isISO8601(),
  body('start_time').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('end_time').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
], classController.createClassSession);

// Get class enrollments
router.get('/:id/enrollments', [
  authenticateToken,
  param('id').isUUID(),
], classController.getClassEnrollments);

// Enroll member in class
router.post('/:id/enrollments', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('member_id').isUUID(),
  body('level_id').optional().isUUID(),
], classController.enrollMember);

// Update enrollment status
router.put('/:id/enrollments/:enrollmentId', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  param('enrollmentId').isUUID(),
  body('status').isIn(['enrolled', 'completed', 'dropped']),
], classController.updateEnrollment);

// Level routes
router.get('/levels/:levelId', [
  authenticateToken,
  param('levelId').isUUID(),
], classController.getLevelById);

router.put('/levels/:levelId', [
  authenticateToken,
  requireAdmin,
  param('levelId').isUUID(),
], classController.updateLevel);

router.delete('/levels/:levelId', [
  authenticateToken,
  requireAdmin,
  param('levelId').isUUID(),
], classController.deleteLevel);

// Session routes
router.get('/sessions/:sessionId', [
  authenticateToken,
  param('sessionId').isUUID(),
], classController.getSessionById);

router.put('/sessions/:sessionId', [
  authenticateToken,
  requireAdmin,
  param('sessionId').isUUID(),
], classController.updateSession);

router.delete('/sessions/:sessionId', [
  authenticateToken,
  requireAdmin,
  param('sessionId').isUUID(),
], classController.deleteSession);

export default router;
