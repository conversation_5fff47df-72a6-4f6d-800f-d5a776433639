import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { ArticleController } from '../controllers/ArticleController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();
const articleController = new ArticleController();

// Get all articles (public endpoint with optional auth for admin features)
router.get('/', articleController.getArticles);

// Get article by ID (public endpoint)
router.get('/:id', [
  param('id').isUUID(),
], articleController.getArticleById);

// Create new article
router.post('/', [
  authenticateToken,
  requireAdmin,
  body('title').isLength({ min: 1 }).trim(),
  body('summary').optional().trim(),
  body('content').isLength({ min: 1 }),
  body('category').isLength({ min: 1 }).trim(),
  body('image_url').optional().isURL(),
  body('status').optional().isIn(['draft', 'published', 'archived']),
  body('featured').optional().isBoolean(),
], articleController.createArticle);

// Update article
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('title').optional().isLength({ min: 1 }).trim(),
  body('summary').optional().trim(),
  body('content').optional().isLength({ min: 1 }),
  body('category').optional().isLength({ min: 1 }).trim(),
  body('image_url').optional().isURL(),
  body('status').optional().isIn(['draft', 'published', 'archived']),
  body('featured').optional().isBoolean(),
], articleController.updateArticle);

// Delete article
router.delete('/:id', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], articleController.deleteArticle);

// Publish article
router.patch('/:id/publish', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], articleController.publishArticle);

// Unpublish article
router.patch('/:id/unpublish', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
], articleController.unpublishArticle);

// Toggle featured status
router.patch('/:id/featured', [
  authenticateToken,
  requireAdmin,
  param('id').isUUID(),
  body('featured').isBoolean(),
], articleController.toggleFeatured);

// Increment view count
router.post('/:id/view', [
  param('id').isUUID(),
], articleController.incrementViewCount);

// Get featured articles (public endpoint)
router.get('/featured/list', articleController.getFeaturedArticles);

// Get articles by category (public endpoint)
router.get('/category/:category', [
  param('category').isLength({ min: 1 }).trim(),
], articleController.getArticlesByCategory);

// Get article categories (public endpoint)
router.get('/categories/list', articleController.getCategories);

export default router;
