import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';
import { hashPassword, generateDefaultPassword } from '../utils/password';
import { ApiResponse, PaginationQuery, MemberCreateRequest } from '../types/api';

export class MemberController {
  async getMembers(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { page = 1, limit = 10, search }: PaginationQuery = req.query;
      
      const skip = (page - 1) * limit;
      const take = Math.min(limit, 100);

      const where: any = { status: 'active' };
      if (search) {
        where.OR = [
          { first_name: { contains: search, mode: 'insensitive' } },
          { last_name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [members, total] = await Promise.all([
        prisma.member.findMany({
          where,
          select: {
            id: true,
            email: true,
            first_name: true,
            last_name: true,
            phone: true,
            status: true,
            role: true,
            join_date: true,
            cell_group: { select: { id: true, name: true } },
            district: { select: { id: true, name: true } },
          },
          orderBy: { last_name: 'asc' },
          skip,
          take,
        }),
        prisma.member.count({ where }),
      ]);

      res.json({
        success: true,
        data: members,
        pagination: { page, limit: take, total, totalPages: Math.ceil(total / take) },
      });
    } catch (error) {
      next(error);
    }
  }

  async getMemberById(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const member = await prisma.member.findUnique({
        where: { id },
        select: {
          id: true, email: true, first_name: true, last_name: true,
          phone: true, address: true, date_of_birth: true, gender: true,
          marital_status: true, join_date: true, status: true, role: true,
          cell_group: { select: { id: true, name: true } },
          district: { select: { id: true, name: true } },
        },
      });

      if (!member) throw createError('Member not found', 404);
      res.json({ success: true, data: member });
    } catch (error) {
      next(error);
    }
  }

  async createMember(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const memberData: MemberCreateRequest = req.body;
      
      const existingMember = await prisma.member.findUnique({
        where: { email: memberData.email },
      });
      if (existingMember) throw createError('Email already exists', 400);

      const defaultPassword = generateDefaultPassword(memberData.date_of_birth);
      const hashedPassword = await hashPassword(defaultPassword);

      const member = await prisma.member.create({
        data: {
          ...memberData,
          password_hash: hashedPassword,
          password_reset_required: true,
          date_of_birth: memberData.date_of_birth ? new Date(memberData.date_of_birth) : null,
        },
      });

      res.status(201).json({ success: true, data: member });
    } catch (error) {
      next(error);
    }
  }

  async updateMember(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const member = await prisma.member.update({
        where: { id },
        data: {
          ...updateData,
          date_of_birth: updateData.date_of_birth ? new Date(updateData.date_of_birth) : undefined,
        },
      });

      res.json({ success: true, data: member });
    } catch (error) {
      next(error);
    }
  }

  async deleteMember(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      await prisma.member.update({
        where: { id },
        data: { status: 'inactive' },
      });
      res.json({ success: true, data: { message: 'Member deleted successfully' } });
    } catch (error) {
      next(error);
    }
  }

  async getMemberAttendance(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const attendance = await prisma.attendanceParticipant.findMany({
        where: { member_id: id },
        include: {
          meeting: {
            select: {
              meeting_date: true, meeting_type: true, topic: true,
              cell_group: { select: { name: true } },
            },
          },
        },
        orderBy: { meeting: { meeting_date: 'desc' } },
      });
      res.json({ success: true, data: attendance });
    } catch (error) {
      next(error);
    }
  }

  async setMemberPassword(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const { password } = req.body;
      const hashedPassword = await hashPassword(password);
      
      await prisma.member.update({
        where: { id },
        data: { password_hash: hashedPassword, password_reset_required: true },
      });
      
      res.json({ success: true, data: { message: 'Password set successfully' } });
    } catch (error) {
      next(error);
    }
  }
}
