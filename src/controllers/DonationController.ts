import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { createError } from '../middleware/errorHandler';

const prisma = new PrismaClient();

// Get all donations with pagination and filtering
export const getDonations = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const project_id = req.query.project_id as string;
    const status = req.query.status as string;
    const search = req.query.search as string;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (project_id) {
      where.project_id = project_id;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (search) {
      where.OR = [
        { donor_name: { contains: search } },
        { donor_email: { contains: search } }
      ];
    }

    // Get total count
    const total = await prisma.donation.count({ where });

    // Get donations with project info
    const donations = await prisma.donation.findMany({
      where,
      include: {
        project: {
          select: {
            id: true,
            title: true,
            status: true
          }
        }
      },
      orderBy: { donated_at: 'desc' },
      skip,
      take: limit
    });

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: donations,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching donations:', error);
    throw createError('Failed to fetch donations', 500);
  }
};

// Get single donation by ID
export const getDonation = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const donation = await prisma.donation.findUnique({
      where: { id },
      include: {
        project: {
          select: {
            id: true,
            title: true,
            description: true,
            target_amount: true,
            status: true
          }
        }
      }
    });

    if (!donation) {
      throw createError('Donation not found', 404);
    }

    res.json({
      success: true,
      data: donation
    });
  } catch (error) {
    console.error('Error fetching donation:', error);
    throw createError('Failed to fetch donation', 500);
  }
};

// Create new donation
export const createDonation = async (req: Request, res: Response) => {
  try {
    const {
      project_id,
      donor_name,
      donor_email,
      donor_phone,
      amount,
      message,
      is_anonymous = false,
      status = 'confirmed'
    } = req.body;

    // Validate required fields
    if (!project_id || !donor_name || !amount) {
      throw createError('Missing required fields: project_id, donor_name, amount', 400);
    }

    // Validate amount is positive
    if (Number(amount) <= 0) {
      throw createError('Donation amount must be greater than 0', 400);
    }

    // Check if project exists and is published
    const project = await prisma.project.findUnique({
      where: { id: project_id }
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    if (!project.is_published) {
      throw createError('Cannot donate to unpublished project', 400);
    }

    if (project.status === 'completed' || project.status === 'cancelled') {
      throw createError('Cannot donate to completed or cancelled project', 400);
    }

    const donation = await prisma.donation.create({
      data: {
        project_id,
        donor_name,
        donor_email,
        donor_phone,
        amount: Number(amount),
        message,
        is_anonymous,
        status
      },
      include: {
        project: {
          select: {
            id: true,
            title: true,
            target_amount: true
          }
        }
      }
    });

    // Update project current_amount if donation is confirmed
    if (status === 'confirmed') {
      await updateProjectCurrentAmount(project_id);
    }

    res.status(201).json({
      success: true,
      data: donation
    });
  } catch (error) {
    console.error('Error creating donation:', error);
    throw createError('Failed to create donation', 500);
  }
};

// Update donation
export const updateDonation = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      donor_name,
      donor_email,
      donor_phone,
      amount,
      message,
      is_anonymous,
      status
    } = req.body;

    // Check if donation exists
    const existingDonation = await prisma.donation.findUnique({
      where: { id }
    });

    if (!existingDonation) {
      throw createError('Donation not found', 404);
    }

    // Validate amount if provided
    if (amount !== undefined && Number(amount) <= 0) {
      throw createError('Donation amount must be greater than 0', 400);
    }

    // Build update data
    const updateData: any = {};
    if (donor_name !== undefined) updateData.donor_name = donor_name;
    if (donor_email !== undefined) updateData.donor_email = donor_email;
    if (donor_phone !== undefined) updateData.donor_phone = donor_phone;
    if (amount !== undefined) updateData.amount = Number(amount);
    if (message !== undefined) updateData.message = message;
    if (is_anonymous !== undefined) updateData.is_anonymous = is_anonymous;
    if (status !== undefined) updateData.status = status;

    const donation = await prisma.donation.update({
      where: { id },
      data: updateData,
      include: {
        project: {
          select: {
            id: true,
            title: true,
            target_amount: true
          }
        }
      }
    });

    // Update project current_amount if status changed
    if (status !== undefined) {
      await updateProjectCurrentAmount(existingDonation.project_id);
    }

    res.json({
      success: true,
      data: donation
    });
  } catch (error) {
    console.error('Error updating donation:', error);
    throw createError('Failed to update donation', 500);
  }
};

// Delete donation
export const deleteDonation = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if donation exists
    const existingDonation = await prisma.donation.findUnique({
      where: { id }
    });

    if (!existingDonation) {
      throw createError('Donation not found', 404);
    }

    const project_id = existingDonation.project_id;

    await prisma.donation.delete({
      where: { id }
    });

    // Update project current_amount
    await updateProjectCurrentAmount(project_id);

    res.json({
      success: true,
      message: 'Donation deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting donation:', error);
    throw createError('Failed to delete donation', 500);
  }
};

// Get donations by project ID
export const getDonationsByProject = async (req: Request, res: Response) => {
  try {
    const { project_id } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string || 'confirmed';

    const skip = (page - 1) * limit;

    // Check if project exists
    const project = await prisma.project.findUnique({
      where: { id: project_id }
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    const where = {
      project_id,
      status
    };

    // Get total count
    const total = await prisma.donation.count({ where });

    // Get donations
    const donations = await prisma.donation.findMany({
      where,
      select: {
        id: true,
        donor_name: true,
        amount: true,
        message: true,
        is_anonymous: true,
        donated_at: true
      },
      orderBy: { donated_at: 'desc' },
      skip,
      take: limit
    });

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: donations,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching project donations:', error);
    throw createError('Failed to fetch project donations', 500);
  }
};

// Helper function to update project current_amount
async function updateProjectCurrentAmount(project_id: string) {
  try {
    // Calculate total confirmed donations
    const result = await prisma.donation.aggregate({
      where: {
        project_id,
        status: 'confirmed'
      },
      _sum: {
        amount: true
      }
    });

    const current_amount = result._sum.amount || 0;

    // Update project
    await prisma.project.update({
      where: { id: project_id },
      data: { current_amount: Number(current_amount) }
    });
  } catch (error) {
    console.error('Error updating project current amount:', error);
  }
}
