import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';
import { ApiResponse, PaginationQuery } from '../types/api';

export class CellGroupController {
  async getCellGroups(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { page = 1, limit = 10, search, district_id }: PaginationQuery & { district_id?: string } = req.query;
      
      const skip = (page - 1) * limit;
      const take = Math.min(limit, 100);

      const where: any = { status: 'active' };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (district_id) {
        where.district_id = district_id;
      }

      const [cellGroups, total] = await Promise.all([
        prisma.cellGroup.findMany({
          where,
          select: {
            id: true,
            name: true,
            description: true,
            meeting_day: true,
            meeting_time: true,
            meeting_location: true,
            status: true,
            created_at: true,
            district: {
              select: { id: true, name: true },
            },
            leader: {
              select: { id: true, first_name: true, last_name: true },
            },
            assistant_leader: {
              select: { id: true, first_name: true, last_name: true },
            },
            _count: {
              select: { cell_group_members: true },
            },
          },
          orderBy: { name: 'asc' },
          skip,
          take,
        }),
        prisma.cellGroup.count({ where }),
      ]);

      res.json({
        success: true,
        data: cellGroups,
        pagination: { page, limit: take, total, totalPages: Math.ceil(total / take) },
      });
    } catch (error) {
      next(error);
    }
  }

  async getCellGroupById(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      const cellGroup = await prisma.cellGroup.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          meeting_day: true,
          meeting_time: true,
          meeting_location: true,
          status: true,
          created_at: true,
          updated_at: true,
          district: {
            select: { id: true, name: true },
          },
          leader: {
            select: { id: true, first_name: true, last_name: true, email: true, phone: true },
          },
          assistant_leader: {
            select: { id: true, first_name: true, last_name: true, email: true, phone: true },
          },
          cell_group_members: {
            select: {
              id: true,
              joined_date: true,
              status: true,
              member: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  phone: true,
                },
              },
            },
            where: { status: 'active' },
          },
          _count: {
            select: { 
              cell_group_members: { where: { status: 'active' } },
              attendance_meetings: true,
            },
          },
        },
      });

      if (!cellGroup) {
        throw createError('Cell group not found', 404);
      }

      res.json({ success: true, data: cellGroup });
    } catch (error) {
      next(error);
    }
  }

  async createCellGroup(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const cellGroupData = req.body;

      // Validate district exists if provided
      if (cellGroupData.district_id) {
        const district = await prisma.district.findUnique({
          where: { id: cellGroupData.district_id },
        });
        if (!district) {
          throw createError('District not found', 400);
        }
      }

      // Validate leader exists if provided
      if (cellGroupData.leader_id) {
        const leader = await prisma.member.findUnique({
          where: { id: cellGroupData.leader_id },
        });
        if (!leader) {
          throw createError('Leader not found', 400);
        }
      }

      // Validate assistant leader exists if provided
      if (cellGroupData.assistant_leader_id) {
        const assistantLeader = await prisma.member.findUnique({
          where: { id: cellGroupData.assistant_leader_id },
        });
        if (!assistantLeader) {
          throw createError('Assistant leader not found', 400);
        }
      }

      const cellGroup = await prisma.cellGroup.create({
        data: cellGroupData,
        select: {
          id: true,
          name: true,
          description: true,
          meeting_day: true,
          meeting_time: true,
          meeting_location: true,
          status: true,
          created_at: true,
        },
      });

      res.status(201).json({ success: true, data: cellGroup });
    } catch (error) {
      next(error);
    }
  }

  async updateCellGroup(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;
      const updateData = req.body;

      // Check if cell group exists
      const existingCellGroup = await prisma.cellGroup.findUnique({
        where: { id },
      });

      if (!existingCellGroup) {
        throw createError('Cell group not found', 404);
      }

      const cellGroup = await prisma.cellGroup.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          name: true,
          description: true,
          meeting_day: true,
          meeting_time: true,
          meeting_location: true,
          status: true,
          updated_at: true,
        },
      });

      res.json({ success: true, data: cellGroup });
    } catch (error) {
      next(error);
    }
  }

  async deleteCellGroup(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      // Check if cell group exists
      const existingCellGroup = await prisma.cellGroup.findUnique({
        where: { id },
      });

      if (!existingCellGroup) {
        throw createError('Cell group not found', 404);
      }

      // Soft delete by updating status
      await prisma.cellGroup.update({
        where: { id },
        data: { status: 'inactive' },
      });

      res.json({ success: true, data: { message: 'Cell group deleted successfully' } });
    } catch (error) {
      next(error);
    }
  }

  async getCellGroupMembers(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      const members = await prisma.cellGroupMember.findMany({
        where: { 
          cell_group_id: id,
          status: 'active',
        },
        select: {
          id: true,
          joined_date: true,
          status: true,
          member: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              phone: true,
              status: true,
            },
          },
        },
        orderBy: {
          member: { last_name: 'asc' },
        },
      });

      res.json({ success: true, data: members });
    } catch (error) {
      next(error);
    }
  }

  async addMembersToCellGroup(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;
      const { member_ids } = req.body;

      // Check if cell group exists
      const cellGroup = await prisma.cellGroup.findUnique({
        where: { id },
      });

      if (!cellGroup) {
        throw createError('Cell group not found', 404);
      }

      // Create cell group memberships
      const memberships = member_ids.map((member_id: string) => ({
        cell_group_id: id,
        member_id,
      }));

      await prisma.cellGroupMember.createMany({
        data: memberships,
        skipDuplicates: true,
      });

      res.json({ success: true, data: { message: 'Members added successfully' } });
    } catch (error) {
      next(error);
    }
  }

  async removeMemberFromCellGroup(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id, memberId } = req.params;

      // Update membership status to inactive
      await prisma.cellGroupMember.updateMany({
        where: {
          cell_group_id: id,
          member_id: memberId,
        },
        data: { status: 'inactive' },
      });

      res.json({ success: true, data: { message: 'Member removed successfully' } });
    } catch (error) {
      next(error);
    }
  }

  async getCellGroupMeetings(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;
      const { page = 1, limit = 10 } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Math.min(Number(limit), 100);

      const [meetings, total] = await Promise.all([
        prisma.attendanceMeeting.findMany({
          where: { 
            cell_group_id: id,
            event_category: 'cell_group',
          },
          select: {
            id: true,
            meeting_date: true,
            meeting_type: true,
            topic: true,
            notes: true,
            location: true,
            offering: true,
            is_realtime: true,
            created_at: true,
            _count: {
              select: {
                participants: true,
                visitors: true,
              },
            },
          },
          orderBy: { meeting_date: 'desc' },
          skip,
          take,
        }),
        prisma.attendanceMeeting.count({
          where: { 
            cell_group_id: id,
            event_category: 'cell_group',
          },
        }),
      ]);

      res.json({
        success: true,
        data: meetings,
        pagination: {
          page: Number(page),
          limit: take,
          total,
          totalPages: Math.ceil(total / take),
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
