import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';
import { ApiResponse, PaginationQuery } from '../types/api';

export class ArticleController {
  async getArticles(req: Request, res: Response, next: NextFunction) {
    try {
      const { 
        page = 1, 
        limit = 10, 
        search, 
        category, 
        status = 'published',
        featured 
      }: PaginationQuery & { 
        category?: string; 
        status?: string; 
        featured?: string;
      } = req.query;
      
      const skip = (page - 1) * limit;
      const take = Math.min(limit, 100);

      const where: any = {};
      
      // Only show published articles for non-authenticated users
      const authHeader = req.headers.authorization;
      const isAuthenticated = authHeader && authHeader.startsWith('Bearer ');
      
      if (!isAuthenticated) {
        where.status = 'published';
      } else if (status) {
        where.status = status;
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { summary: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (category) {
        where.category = category;
      }

      if (featured !== undefined) {
        where.featured = featured === 'true';
      }

      const [articles, total] = await Promise.all([
        prisma.article.findMany({
          where,
          select: {
            id: true,
            title: true,
            summary: true,
            category: true,
            image_url: true,
            status: true,
            featured: true,
            published_at: true,
            view_count: true,
            created_at: true,
            author: {
              select: { id: true, first_name: true, last_name: true },
            },
          },
          orderBy: [
            { featured: 'desc' },
            { published_at: 'desc' },
            { created_at: 'desc' },
          ],
          skip,
          take,
        }),
        prisma.article.count({ where }),
      ]);

      res.json({
        success: true,
        data: articles,
        pagination: { page, limit: take, total, totalPages: Math.ceil(total / take) },
      });
    } catch (error) {
      next(error);
    }
  }

  async getArticleById(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      const article = await prisma.article.findUnique({
        where: { id },
        select: {
          id: true,
          title: true,
          summary: true,
          content: true,
          category: true,
          image_url: true,
          status: true,
          featured: true,
          published_at: true,
          view_count: true,
          created_at: true,
          updated_at: true,
          author: {
            select: { id: true, first_name: true, last_name: true },
          },
        },
      });

      if (!article) {
        throw createError('Article not found', 404);
      }

      // Only show published articles for non-authenticated users
      const authHeader = req.headers.authorization;
      const isAuthenticated = authHeader && authHeader.startsWith('Bearer ');
      
      if (!isAuthenticated && article.status !== 'published') {
        throw createError('Article not found', 404);
      }

      res.json({ success: true, data: article });
    } catch (error) {
      next(error);
    }
  }

  async createArticle(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const articleData = {
        ...req.body,
        author_id: req.user!.id,
      };

      // Set published_at if status is published
      if (articleData.status === 'published') {
        articleData.published_at = new Date();
      }

      const article = await prisma.article.create({
        data: articleData,
        select: {
          id: true,
          title: true,
          summary: true,
          category: true,
          status: true,
          featured: true,
          created_at: true,
        },
      });

      res.status(201).json({ success: true, data: article });
    } catch (error) {
      next(error);
    }
  }

  async updateArticle(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;
      const updateData = req.body;

      // Check if article exists
      const existingArticle = await prisma.article.findUnique({
        where: { id },
      });

      if (!existingArticle) {
        throw createError('Article not found', 404);
      }

      // Set published_at if status is being changed to published
      if (updateData.status === 'published' && existingArticle.status !== 'published') {
        updateData.published_at = new Date();
      }

      const article = await prisma.article.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          title: true,
          summary: true,
          category: true,
          status: true,
          featured: true,
          updated_at: true,
        },
      });

      res.json({ success: true, data: article });
    } catch (error) {
      next(error);
    }
  }

  async deleteArticle(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      // Check if article exists
      const existingArticle = await prisma.article.findUnique({
        where: { id },
      });

      if (!existingArticle) {
        throw createError('Article not found', 404);
      }

      await prisma.article.delete({
        where: { id },
      });

      res.json({ success: true, data: { message: 'Article deleted successfully' } });
    } catch (error) {
      next(error);
    }
  }

  async publishArticle(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      const article = await prisma.article.update({
        where: { id },
        data: {
          status: 'published',
          published_at: new Date(),
        },
        select: {
          id: true,
          title: true,
          status: true,
          published_at: true,
        },
      });

      res.json({ success: true, data: article });
    } catch (error) {
      next(error);
    }
  }

  async unpublishArticle(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      const article = await prisma.article.update({
        where: { id },
        data: {
          status: 'draft',
        },
        select: {
          id: true,
          title: true,
          status: true,
        },
      });

      res.json({ success: true, data: article });
    } catch (error) {
      next(error);
    }
  }

  async toggleFeatured(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;
      const { featured } = req.body;

      const article = await prisma.article.update({
        where: { id },
        data: { featured },
        select: {
          id: true,
          title: true,
          featured: true,
        },
      });

      res.json({ success: true, data: article });
    } catch (error) {
      next(error);
    }
  }

  async incrementViewCount(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { id } = req.params;

      await prisma.article.update({
        where: { id },
        data: {
          view_count: {
            increment: 1,
          },
        },
      });

      res.json({ success: true, data: { message: 'View count incremented' } });
    } catch (error) {
      next(error);
    }
  }

  async getFeaturedArticles(req: Request, res: Response, next: NextFunction) {
    try {
      const { limit = 5 } = req.query;
      const take = Math.min(Number(limit), 20);

      const articles = await prisma.article.findMany({
        where: {
          status: 'published',
          featured: true,
        },
        select: {
          id: true,
          title: true,
          summary: true,
          category: true,
          image_url: true,
          published_at: true,
          view_count: true,
          author: {
            select: { first_name: true, last_name: true },
          },
        },
        orderBy: { published_at: 'desc' },
        take,
      });

      res.json({ success: true, data: articles });
    } catch (error) {
      next(error);
    }
  }

  async getArticlesByCategory(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('Validation failed', 400);
      }

      const { category } = req.params;
      const { page = 1, limit = 10 } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Math.min(Number(limit), 100);

      const [articles, total] = await Promise.all([
        prisma.article.findMany({
          where: {
            category,
            status: 'published',
          },
          select: {
            id: true,
            title: true,
            summary: true,
            category: true,
            image_url: true,
            published_at: true,
            view_count: true,
            author: {
              select: { first_name: true, last_name: true },
            },
          },
          orderBy: { published_at: 'desc' },
          skip,
          take,
        }),
        prisma.article.count({
          where: {
            category,
            status: 'published',
          },
        }),
      ]);

      res.json({
        success: true,
        data: articles,
        pagination: {
          page: Number(page),
          limit: take,
          total,
          totalPages: Math.ceil(total / take),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async getCategories(req: Request, res: Response, next: NextFunction) {
    try {
      const categories = await prisma.article.groupBy({
        by: ['category'],
        where: {
          status: 'published',
        },
        _count: {
          category: true,
        },
        orderBy: {
          _count: {
            category: 'desc',
          },
        },
      });

      const formattedCategories = categories.map(cat => ({
        name: cat.category,
        count: cat._count.category,
      }));

      res.json({ success: true, data: formattedCategories });
    } catch (error) {
      next(error);
    }
  }
}
