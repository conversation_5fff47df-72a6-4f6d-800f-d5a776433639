// API Client for Node.js Backend
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api";

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    stack?: string;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    role: string;
    role_level: number;
  };
  tokens: {
    access_token: string;
    refresh_token: string;
  };
}

class ApiClient {
  private baseURL: string;
  private accessToken: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    // Load token from localStorage if available
    if (typeof window !== "undefined") {
      this.accessToken = localStorage.getItem("access_token");
    }
  }

  // Set authentication token
  setToken(token: string) {
    this.accessToken = token;
    if (typeof window !== "undefined") {
      localStorage.setItem("access_token", token);
    }
  }

  // Clear authentication token
  clearToken() {
    this.accessToken = null;
    if (typeof window !== "undefined") {
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
    }
  }

  // Get authentication headers
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    if (this.accessToken) {
      headers["Authorization"] = `Bearer ${this.accessToken}`;
    }

    return headers;
  }

  // Generic API request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers,
        },
      });

      const data: ApiResponse<T> = await response.json();

      // Handle token refresh if needed
      if (!data.success && data.error?.message === "Token expired") {
        const refreshed = await this.refreshToken();
        if (refreshed) {
          // Retry the original request
          return this.request(endpoint, options);
        }
      }

      return data;
    } catch (error) {
      console.error("API request failed:", error);
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : "Network error",
        },
      };
    }
  }

  // Refresh access token
  private async refreshToken(): Promise<boolean> {
    try {
      const refreshToken =
        typeof window !== "undefined"
          ? localStorage.getItem("refresh_token")
          : null;

      if (!refreshToken) return false;

      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      const data: ApiResponse<{ access_token: string; refresh_token: string }> =
        await response.json();

      if (data.success && data.data) {
        this.setToken(data.data.access_token);
        if (typeof window !== "undefined") {
          localStorage.setItem("refresh_token", data.data.refresh_token);
        }
        return true;
      }

      return false;
    } catch (error) {
      console.error("Token refresh failed:", error);
      return false;
    }
  }

  // Authentication methods
  async loginAdmin(
    email: string,
    password: string
  ): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>("/auth/admin/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });

    if (response.success && response.data) {
      this.setToken(response.data.tokens.access_token);
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "refresh_token",
          response.data.tokens.refresh_token
        );
      }
    }

    return response;
  }

  async loginMember(
    email: string,
    password: string
  ): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>("/auth/member/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });

    if (response.success && response.data) {
      this.setToken(response.data.tokens.access_token);
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "refresh_token",
          response.data.tokens.refresh_token
        );
      }
    }

    return response;
  }

  async checkMember(email: string): Promise<ApiResponse<any>> {
    return this.request("/auth/member/check", {
      method: "POST",
      body: JSON.stringify({ email }),
    });
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    return this.request("/auth/me");
  }

  async logout(): Promise<ApiResponse<any>> {
    const response = await this.request("/auth/logout", {
      method: "POST",
    });
    this.clearToken();
    return response;
  }

  // Districts API
  async getDistricts(params?: {
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());

    const query = queryParams.toString();
    return this.request(`/districts${query ? `?${query}` : ""}`);
  }

  async getDistrict(id: string): Promise<ApiResponse<any>> {
    return this.request(`/districts/${id}`);
  }

  async createDistrict(data: any): Promise<ApiResponse<any>> {
    return this.request("/districts", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateDistrict(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request(`/districts/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteDistrict(id: string): Promise<ApiResponse<any>> {
    return this.request(`/districts/${id}`, {
      method: "DELETE",
    });
  }

  // Members API
  async getMembers(params?: {
    page?: number;
    limit?: number;
    search?: string;
    district_id?: string;
    cell_group_id?: string;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.search) queryParams.append("search", params.search);
    if (params?.district_id)
      queryParams.append("district_id", params.district_id);
    if (params?.cell_group_id)
      queryParams.append("cell_group_id", params.cell_group_id);

    const query = queryParams.toString();
    return this.request(`/members${query ? `?${query}` : ""}`);
  }

  async getMember(id: string): Promise<ApiResponse<any>> {
    return this.request(`/members/${id}`);
  }

  async createMember(data: any): Promise<ApiResponse<any>> {
    return this.request("/members", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateMember(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request(`/members/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteMember(id: string): Promise<ApiResponse<any>> {
    return this.request(`/members/${id}`, {
      method: "DELETE",
    });
  }

  // Cell Groups API
  async getCellGroups(params?: {
    page?: number;
    limit?: number;
    district_id?: string;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.district_id)
      queryParams.append("district_id", params.district_id);

    const query = queryParams.toString();
    return this.request(`/cell-groups${query ? `?${query}` : ""}`);
  }

  async getCellGroup(id: string): Promise<ApiResponse<any>> {
    return this.request(`/cell-groups/${id}`);
  }

  async createCellGroup(data: any): Promise<ApiResponse<any>> {
    return this.request("/cell-groups", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateCellGroup(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request(`/cell-groups/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteCellGroup(id: string): Promise<ApiResponse<any>> {
    return this.request(`/cell-groups/${id}`, {
      method: "DELETE",
    });
  }

  // Attendance API
  async getAttendanceMeetings(params?: {
    page?: number;
    limit?: number;
    cell_group_id?: string;
    date_from?: string;
    date_to?: string;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.cell_group_id)
      queryParams.append("cell_group_id", params.cell_group_id);
    if (params?.date_from) queryParams.append("date_from", params.date_from);
    if (params?.date_to) queryParams.append("date_to", params.date_to);

    const query = queryParams.toString();
    return this.request(`/attendance/meetings${query ? `?${query}` : ""}`);
  }

  async getAttendanceMeeting(id: string): Promise<ApiResponse<any>> {
    return this.request(`/attendance/meetings/${id}`);
  }

  async createAttendanceMeeting(data: any): Promise<ApiResponse<any>> {
    return this.request("/attendance/meetings", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateAttendanceMeeting(
    id: string,
    data: any
  ): Promise<ApiResponse<any>> {
    return this.request(`/attendance/meetings/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteAttendanceMeeting(id: string): Promise<ApiResponse<any>> {
    return this.request(`/attendance/meetings/${id}`, {
      method: "DELETE",
    });
  }

  // Projects API
  async getProjects(params?: {
    page?: number;
    limit?: number;
    status?: string;
    is_published?: boolean;
    search?: string;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.status) queryParams.append("status", params.status);
    if (params?.is_published !== undefined)
      queryParams.append("is_published", params.is_published.toString());
    if (params?.search) queryParams.append("search", params.search);

    const query = queryParams.toString();
    return this.request(`/projects${query ? `?${query}` : ""}`);
  }

  async getPublishedProjects(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.search) queryParams.append("search", params.search);

    const query = queryParams.toString();
    return this.request(`/projects/published${query ? `?${query}` : ""}`);
  }

  async getProject(id: string): Promise<ApiResponse<any>> {
    return this.request(`/projects/${id}`);
  }

  async createProject(data: any): Promise<ApiResponse<any>> {
    return this.request("/projects", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateProject(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request(`/projects/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: string): Promise<ApiResponse<any>> {
    return this.request(`/projects/${id}`, {
      method: "DELETE",
    });
  }

  // Donations API
  async getDonations(params?: {
    page?: number;
    limit?: number;
    project_id?: string;
    status?: string;
    search?: string;
  }): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.project_id) queryParams.append("project_id", params.project_id);
    if (params?.status) queryParams.append("status", params.status);
    if (params?.search) queryParams.append("search", params.search);

    const query = queryParams.toString();
    return this.request(`/donations${query ? `?${query}` : ""}`);
  }

  async getDonationsByProject(
    project_id: string,
    params?: {
      page?: number;
      limit?: number;
      status?: string;
    }
  ): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.status) queryParams.append("status", params.status);

    const query = queryParams.toString();
    return this.request(
      `/donations/project/${project_id}${query ? `?${query}` : ""}`
    );
  }

  async createDonation(data: any): Promise<ApiResponse<any>> {
    return this.request("/donations", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateDonation(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request(`/donations/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteDonation(id: string): Promise<ApiResponse<any>> {
    return this.request(`/donations/${id}`, {
      method: "DELETE",
    });
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export types
export type { LoginResponse };
