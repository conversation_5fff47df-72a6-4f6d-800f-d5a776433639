@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --font-outfit: 'Outfit', sans-serif;

  /* Colors */
  --color-current: currentColor;
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-black: #101828;

  --color-brand-25: #f2f7ff;
  --color-brand-50: #ecf3ff;
  --color-brand-100: #dde9ff;
  --color-brand-200: #c2d6ff;
  --color-brand-300: #9cb9ff;
  --color-brand-400: #7592ff;
  --color-brand-500: #465fff;
  --color-brand-600: #3641f5;
  --color-brand-700: #2a31d8;
  --color-brand-800: #252dae;
  --color-brand-900: #262e89;
  --color-brand-950: #161950;

  --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f2f4f7;
  --color-gray-200: #e4e7ec;
  --color-gray-300: #d0d5dd;
  --color-gray-400: #98a2b3;
  --color-gray-500: #667085;
  --color-gray-600: #475467;
  --color-gray-700: #344054;
  --color-gray-800: #1d2939;
  --color-gray-900: #101828;
  --color-gray-950: #0c111d;
  --color-gray-dark: #1a2231;

  --color-success-25: #f6fef9;
  --color-success-50: #ecfdf3;
  --color-success-100: #d1fadf;
  --color-success-200: #a6f4c5;
  --color-success-300: #6ce9a6;
  --color-success-400: #32d583;
  --color-success-500: #12b76a;
  --color-success-600: #039855;
  --color-success-700: #027a48;
  --color-success-800: #05603a;
  --color-success-900: #054f31;
  --color-success-950: #053321;

  --color-error-25: #fffbfa;
  --color-error-50: #fef3f2;
  --color-error-100: #fee4e2;
  --color-error-200: #fecdca;
  --color-error-300: #fda29b;
  --color-error-400: #f97066;
  --color-error-500: #f04438;
  --color-error-600: #d92d20;
  --color-error-700: #b42318;
  --color-error-800: #912018;
  --color-error-900: #7a271a;
  --color-error-950: #55160c;

  --color-warning-25: #fffcf5;
  --color-warning-50: #fffaeb;
  --color-warning-100: #fef0c7;
  --color-warning-200: #fedf89;
  --color-warning-300: #fec84b;
  --color-warning-400: #fdb022;
  --color-warning-500: #f79009;
  --color-warning-600: #dc6803;
  --color-warning-700: #b54708;
  --color-warning-800: #93370d;
  --color-warning-900: #7a2e0e;
  --color-warning-950: #4e1d09;

  /* Shadows */
  --shadow-theme-md:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  --shadow-theme-lg:
    0px 12px 16px -4px rgba(16, 24, 40, 0.08),
    0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  --shadow-theme-sm:
    0px 1px 3px 0px rgba(16, 24, 40, 0.1),
    0px 1px 2px 0px rgba(16, 24, 40, 0.06);
  --shadow-theme-xs: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  --shadow-theme-xl:
    0px 20px 24px -4px rgba(16, 24, 40, 0.08),
    0px 8px 8px -4px rgba(16, 24, 40, 0.03);
  --shadow-focus-ring: 0px 0px 0px 4px rgba(70, 95, 255, 0.12);
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
  body {
    @apply relative font-normal z-1 bg-gray-50 font-outfit;
  }
}

@layer components {
  .btn-primary {
    @apply bg-brand-500 text-white px-4 py-2 rounded-md hover:bg-brand-600 transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-opacity-50;
  }

  .card {
    @apply bg-white p-6 rounded-lg shadow-md;
  }

  /* TailAdmin Components */
  .menu-item {
    @apply relative flex items-center w-full gap-3 px-3 py-2 font-medium rounded-lg text-sm;
  }

  .menu-item-active {
    @apply bg-brand-50 text-brand-500 dark:bg-opacity-10 dark:bg-brand-500 dark:text-brand-400;
  }

  .menu-item-inactive {
    @apply text-gray-700 hover:bg-gray-100 group-hover:text-gray-700 dark:text-gray-300 dark:hover:bg-white dark:hover:bg-opacity-5 dark:hover:text-gray-300;
  }

  .menu-item-icon {
    @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400 h-5 w-5 flex-shrink-0;
  }

  .menu-item-icon-active {
    @apply text-brand-500 dark:text-brand-400;
  }

  .menu-item-icon-inactive {
    @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300;
  }

  /* Collapsed sidebar menu items */
  .sidebar:not(.w-72) .menu-item {
    @apply justify-center px-0;
  }

  /* Ensure icons maintain their size in collapsed sidebar */
  .sidebar:not(.w-72) .menu-item svg {
    @apply h-5 w-5 flex-shrink-0;
  }

  /* No additional padding needed as the layout handles spacing */

  /* Ensure all sidebar icons have consistent size */
  .sidebar svg {
    @apply h-5 w-5 flex-shrink-0;
  }

  .no-scrollbar {
    /* Chrome, Safari and Opera */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .custom-scrollbar {
    &::-webkit-scrollbar {
      @apply w-1.5 h-1.5;
    }

    &::-webkit-scrollbar-track {
      @apply rounded-full;
    }

    &::-webkit-scrollbar-thumb {
      @apply bg-gray-200 rounded-full dark:bg-gray-700;
    }
  }

  /* Mobile Navigation Floating Button */
  .mobile-nav-button {
    @apply fixed bottom-6 right-6 z-9999 flex h-14 w-14 items-center justify-center rounded-full bg-brand-500 text-white shadow-lg transition-all hover:bg-brand-600 focus:outline-none lg:hidden;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(70, 95, 255, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(70, 95, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(70, 95, 255, 0);
    }
  }
}
