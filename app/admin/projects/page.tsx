'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { apiClient } from '../../lib/api-client';
import Layout from '../../components/layout/Layout';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { useAuth } from '../../contexts/AuthContext';

type Project = {
  id: string;
  title: string;
  description: string;
  image_url?: string;
  event_date: string;
  target_amount: number;
  current_amount: number;
  progress: number;
  status: string;
  is_published: boolean;
  created_at: string;
  creator: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  donations_count: number;
};

export default function AdminProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const { user, isAdmin } = useAuth();

  useEffect(() => {
    if (!isAdmin) {
      setError('Access denied. Admin privileges required.');
      setLoading(false);
      return;
    }
    fetchProjects();
  }, [isAdmin, filter]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {};
      if (filter !== 'all') {
        if (filter === 'published') {
          params.is_published = true;
        } else if (filter === 'draft') {
          params.is_published = false;
        } else {
          params.status = filter;
        }
      }

      const response = await apiClient.getProjects(params);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch projects');
      }

      if (response.data) {
        setProjects(response.data);
      }
    } catch (error: any) {
      console.error('Error fetching projects:', error);
      setError(error.message || 'Failed to fetch projects');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProject = async (id: string) => {
    if (!confirm('Are you sure you want to delete this project?')) {
      return;
    }

    try {
      const response = await apiClient.deleteProject(id);
      if (response.success) {
        setProjects(projects.filter(p => p.id !== id));
      } else {
        alert(response.error?.message || 'Failed to delete project');
      }
    } catch (error: any) {
      console.error('Error deleting project:', error);
      alert(error.message || 'Failed to delete project');
    }
  };

  const handleTogglePublish = async (project: Project) => {
    try {
      const response = await apiClient.updateProject(project.id, {
        is_published: !project.is_published
      });

      if (response.success) {
        setProjects(projects.map(p => 
          p.id === project.id 
            ? { ...p, is_published: !p.is_published }
            : p
        ));
      } else {
        alert(response.error?.message || 'Failed to update project');
      }
    } catch (error: any) {
      console.error('Error updating project:', error);
      alert(error.message || 'Failed to update project');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (!isAdmin) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600">You need admin privileges to access this page.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Project Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage church projects and events
            </p>
          </div>
          <Link href="/admin/projects/add">
            <Button>
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Project
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <div className="flex space-x-4">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="all">All Projects</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-500 mb-4">{error}</div>
            <Button onClick={fetchProjects} variant="outline">
              Try Again
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {projects.map((project) => (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                {/* Project Image */}
                {project.image_url && (
                  <div className="aspect-video bg-gray-200 rounded-t-lg overflow-hidden">
                    <img
                      src={project.image_url}
                      alt={project.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}

                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
                      {project.title}
                    </h3>
                    <div className="flex space-x-2">
                      <Badge
                        variant={project.is_published ? 'success' : 'warning'}
                        size="sm"
                      >
                        {project.is_published ? 'Published' : 'Draft'}
                      </Badge>
                      <Badge
                        variant={
                          project.status === 'completed' ? 'success' :
                          project.status === 'cancelled' ? 'danger' : 'default'
                        }
                        size="sm"
                      >
                        {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                      </Badge>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                    {project.description}
                  </p>

                  {/* Event Date */}
                  <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    📅 {formatDate(project.event_date)}
                  </div>

                  {/* Progress */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600 dark:text-gray-400">Progress</span>
                      <span className="font-medium">{project.progress.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-brand-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(project.progress, 100)}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm mt-2">
                      <span className="text-gray-600 dark:text-gray-400">
                        {formatCurrency(project.current_amount)}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        {formatCurrency(project.target_amount)}
                      </span>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <span>💰 {project.donations_count} donations</span>
                    <span>👤 {project.creator.first_name} {project.creator.last_name}</span>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2">
                    <Link href={`/admin/projects/${project.id}`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full">
                        View
                      </Button>
                    </Link>
                    <Link href={`/admin/projects/${project.id}/edit`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full">
                        Edit
                      </Button>
                    </Link>
                    <Button
                      variant={project.is_published ? 'warning' : 'success'}
                      size="sm"
                      onClick={() => handleTogglePublish(project)}
                    >
                      {project.is_published ? 'Unpublish' : 'Publish'}
                    </Button>
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={() => handleDeleteProject(project.id)}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </Card>
            ))}

            {projects.length === 0 && (
              <div className="col-span-full text-center py-12">
                <div className="text-gray-500 dark:text-gray-400 mb-4">
                  No projects found
                </div>
                <Link href="/admin/projects/add">
                  <Button>Create Your First Project</Button>
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </Layout>
  );
}
