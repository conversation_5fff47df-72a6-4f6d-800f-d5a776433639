'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { apiClient } from '../../../lib/api-client';
import Layout from '../../../components/layout/Layout';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import Badge from '../../../components/ui/Badge';
import { useAuth } from '../../../contexts/AuthContext';

type Project = {
  id: string;
  title: string;
  description: string;
  image_url?: string;
  event_date: string;
  target_amount: number;
  current_amount: number;
  progress: number;
  status: string;
  is_published: boolean;
  created_at: string;
  creator: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  donations: Array<{
    id: string;
    donor_name: string;
    amount: number;
    message?: string;
    is_anonymous: boolean;
    donated_at: string;
  }>;
  donations_count: number;
};

export default function ProjectDetailPage() {
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAdmin } = useAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  useEffect(() => {
    if (!isAdmin) {
      setError('Access denied. Admin privileges required.');
      setLoading(false);
      return;
    }
    if (projectId) {
      fetchProject();
    }
  }, [isAdmin, projectId]);

  const fetchProject = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.getProject(projectId);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch project');
      }

      if (response.data) {
        setProject(response.data);
      }
    } catch (error: any) {
      console.error('Error fetching project:', error);
      setError(error.message || 'Failed to fetch project');
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePublish = async () => {
    if (!project) return;

    try {
      const response = await apiClient.updateProject(project.id, {
        is_published: !project.is_published
      });

      if (response.success) {
        setProject({ ...project, is_published: !project.is_published });
      } else {
        alert(response.error?.message || 'Failed to update project');
      }
    } catch (error: any) {
      console.error('Error updating project:', error);
      alert(error.message || 'Failed to update project');
    }
  };

  const handleDeleteProject = async () => {
    if (!project) return;
    
    if (!confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await apiClient.deleteProject(project.id);
      if (response.success) {
        router.push('/admin/projects');
      } else {
        alert(response.error?.message || 'Failed to delete project');
      }
    } catch (error: any) {
      console.error('Error deleting project:', error);
      alert(error.message || 'Failed to delete project');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!isAdmin) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600">You need admin privileges to access this page.</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500"></div>
        </div>
      </Layout>
    );
  }

  if (error || !project) {
    return (
      <Layout>
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">{error || 'Project not found'}</div>
          <Link href="/admin/projects">
            <Button variant="outline">Back to Projects</Button>
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Link href="/admin/projects" className="text-brand-500 hover:text-brand-600 mb-2 inline-block">
              ← Back to Projects
            </Link>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {project.title}
            </h1>
            <div className="flex items-center space-x-4 mt-2">
              <Badge
                variant={project.is_published ? 'success' : 'warning'}
                size="sm"
              >
                {project.is_published ? 'Published' : 'Draft'}
              </Badge>
              <Badge
                variant={
                  project.status === 'completed' ? 'success' :
                  project.status === 'cancelled' ? 'danger' : 'default'
                }
                size="sm"
              >
                {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
              </Badge>
            </div>
          </div>
          <div className="flex space-x-2">
            <Link href={`/admin/projects/${project.id}/edit`}>
              <Button variant="outline">Edit</Button>
            </Link>
            <Button
              variant={project.is_published ? 'warning' : 'success'}
              onClick={handleTogglePublish}
            >
              {project.is_published ? 'Unpublish' : 'Publish'}
            </Button>
            <Button variant="danger" onClick={handleDeleteProject}>
              Delete
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Project Image */}
            {project.image_url && (
              <Card>
                <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
                  <img
                    src={project.image_url}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              </Card>
            )}

            {/* Project Details */}
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Project Details
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h3>
                  <p className="mt-1 text-gray-900 dark:text-white whitespace-pre-wrap">
                    {project.description}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Event Date</h3>
                  <p className="mt-1 text-gray-900 dark:text-white">
                    {formatDate(project.event_date)}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Created By</h3>
                  <p className="mt-1 text-gray-900 dark:text-white">
                    {project.creator.first_name} {project.creator.last_name} ({project.creator.email})
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Created At</h3>
                  <p className="mt-1 text-gray-900 dark:text-white">
                    {formatDate(project.created_at)}
                  </p>
                </div>
              </div>
            </Card>

            {/* Donations List */}
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Recent Donations ({project.donations_count})
              </h2>
              {project.donations.length > 0 ? (
                <div className="space-y-4">
                  {project.donations.slice(0, 10).map((donation) => (
                    <div key={donation.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {donation.is_anonymous ? 'Anonymous' : donation.donor_name}
                        </p>
                        {donation.message && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            "{donation.message}"
                          </p>
                        )}
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {formatDate(donation.donated_at)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">
                          {formatCurrency(donation.amount)}
                        </p>
                      </div>
                    </div>
                  ))}
                  {project.donations.length > 10 && (
                    <p className="text-center text-gray-500 dark:text-gray-400 text-sm">
                      And {project.donations.length - 10} more donations...
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                  No donations yet
                </p>
              )}
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Progress Card */}
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Funding Progress
              </h2>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600 dark:text-gray-400">Progress</span>
                    <span className="font-medium">{project.progress.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-brand-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(project.progress, 100)}%` }}
                    ></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Raised</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(project.current_amount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Target</span>
                    <span className="font-semibold">
                      {formatCurrency(project.target_amount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Remaining</span>
                    <span className="font-semibold text-orange-600">
                      {formatCurrency(Math.max(0, project.target_amount - project.current_amount))}
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Quick Actions */}
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Quick Actions
              </h2>
              <div className="space-y-2">
                <Link href={`/admin/projects/${project.id}/donations`} className="block">
                  <Button variant="outline" className="w-full">
                    Manage Donations
                  </Button>
                </Link>
                <Link href={`/admin/projects/${project.id}/edit`} className="block">
                  <Button variant="outline" className="w-full">
                    Edit Project
                  </Button>
                </Link>
                {project.is_published && (
                  <Link href={`/member/projects/${project.id}`} className="block">
                    <Button variant="outline" className="w-full">
                      View as Member
                    </Button>
                  </Link>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
}
