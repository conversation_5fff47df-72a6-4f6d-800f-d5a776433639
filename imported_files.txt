../../../../../../../components/layout/Layout
../../../../../../../components/ui/Button
../../../../../../../components/ui/Card
../../../../../../../components/ui/Table
../../../../../../../lib/supabase
../../../../../../../types/class
../../../../../../components/layout/Layout
../../../../../../components/ui/Button
../../../../../../components/ui/Card
../../../../../../components/ui/Input
../../../../../../components/ui/Select
../../../../../../lib/supabase
../../../../../../types/class
../../../../../components/layout/Layout
../../../../../components/ui/Button
../../../../../components/ui/Card
../../../../../components/ui/Input
../../../../../components/ui/Table
../../../../../lib/supabase
../../../../../types/class
../../../../components/Header
../../../../components/RichTextEditor
../../../../components/layout/Layout
../../../../components/ui/Button
../../../../components/ui/Card
../../../../components/ui/Input
../../../../components/ui/Select
../../../../components/ui/Table
../../../../lib/supabase
../../../../types/class
../../../attendance/components/EventCategorySelector
../../../components/Header
../../../components/MemberForm
../../../components/QRCodeGenerator
../../../components/RealtimeAttendanceStats
../../../components/RichTextEditor
../../../components/layout/Layout
../../../components/ui/Button
../../../components/ui/Card
../../../components/ui/Input
../../../components/ui/Select
../../../contexts/AuthContext
../../../lib/role-utils
../../../lib/supabase
../../../types/class
../../../types/ministry
../../components/CellGroupForm
../../components/DistrictForm
../../components/Header
../../components/MemberForm
../../components/MemberTokenManager
../../components/MinistryForm
../../components/ProtectedRoute
../../components/QRCodeGenerator
../../components/layout/Layout
../../components/ui/Badge
../../components/ui/Button
../../components/ui/Card
../../components/ui/Input
../../components/ui/Select
../../components/ui/Table
../../contexts/AuthContext
../../lib/role-utils
../../lib/supabase
../../types/class
../../types/ministry
../components/ContextSelector
../components/EventCategorySelector
../components/Header
../components/MinistryForm
../components/ProtectedRoute
../components/QRCodeScanner
../components/forms/DynamicAttendanceForm
../components/layout/Layout
../components/ui/Badge
../components/ui/Button
../components/ui/Card
../components/ui/Input
../components/ui/Table
../contexts/AuthContext
../lib/supabase
../types/class
../types/ministry
../utils/attendanceUtils
../utils/passwordUtils
./BaseAttendanceForm
./CellGroupAttendanceForm
./ClassAttendanceForm
./ClassDetailsForm
./Header
./MinistryAttendanceForm
./MinistryDetailsForm
./OtherAttendanceForm
./OtherDetailsForm
./PrayerAttendanceForm
./PrayerDetailsForm
./ServiceAttendanceForm
./ServiceDetailsForm
./Sidebar
./VisitorsForm
./api-client
./components/AttendanceTrendChart
./components/layout/MobileOptimization
./contexts/AuthContext
./page.client
@/app/contexts/AuthContext
@supabase/auth-helpers-nextjs
html5-qrcode
next
next/dynamic
next/font/google
next/headers
next/link
next/navigation
next/server
qrcode
react
tailwind-merge
