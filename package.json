{"name": "church-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.38.4", "@tailwindcss/forms": "^0.5.10", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "bcryptjs": "^3.0.2", "eslint": "^8.53.0", "eslint-config-next": "^14.0.2", "html5-qrcode": "^2.3.8", "jspdf": "^2.5.1", "next": "^14.0.0", "postcss": "^8.4.31", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-quill": "^2.0.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/prop-types": "^15.7.14"}}