{"name": "church-management-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:studio": "npx prisma studio", "migrate:supabase": "node scripts/migrate-from-supabase.js", "analyze:supabase": "node scripts/extract-supabase-data.js", "pull:supabase": "node scripts/pull-supabase-data.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.14.0", "@supabase/supabase-js": "^2.55.0", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "mysql2": "^3.14.3", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.3.0", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "nodemon": "^3.1.10", "prisma": "^6.14.0", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}