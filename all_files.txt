./app/admin/articles/add/page.tsx
./app/admin/articles/edit/[id]/page.tsx
./app/admin/articles/page.tsx
./app/admin/classes/enrollments/api-client.ts
./app/admin/classes/enrollments/page.tsx
./app/admin/classes/page.tsx
./app/admin/documents/baptism/page.tsx
./app/admin/events/add/page.tsx
./app/admin/events/calendar/page.tsx
./app/admin/events/page.tsx
./app/admin/page.client.tsx
./app/admin/page.tsx
./app/admin/roles/page.tsx
./app/admin/users/page.tsx
./app/admin/users/permissions/page.tsx
./app/api/admin/set-admin-role/route.ts
./app/api/admin/set-user-role/route.ts
./app/api/auth/admin-register/route.ts
./app/api/auth/check-member/route.ts
./app/api/auth/generate-token/route.ts
./app/api/auth/invalidate-tokens/route.ts
./app/api/auth/reset-password/route.ts
./app/api/auth/set-default-password/route.ts
./app/api/auth/update-password-flag/route.ts
./app/api/auth/verify-password-set/route.ts
./app/api/auth/verify-password/route.ts
./app/api/enrollments/route.ts
./app/api/generate/route.ts
./app/api/new/route.ts
./app/attendance/[id]/page.tsx
./app/attendance/[id]/realtime/page.tsx
./app/attendance/components/AttendanceTrendChart.tsx
./app/attendance/components/ContextSelector.tsx
./app/attendance/components/EventCategorySelector.tsx
./app/attendance/components/forms/BaseAttendanceForm.tsx
./app/attendance/components/forms/CellGroupAttendanceForm.tsx
./app/attendance/components/forms/ClassAttendanceForm.tsx
./app/attendance/components/forms/ClassDetailsForm.tsx
./app/attendance/components/forms/DynamicAttendanceForm.tsx
./app/attendance/components/forms/MinistryAttendanceForm.tsx
./app/attendance/components/forms/MinistryDetailsForm.tsx
./app/attendance/components/forms/OtherAttendanceForm.tsx
./app/attendance/components/forms/OtherDetailsForm.tsx
./app/attendance/components/forms/PrayerAttendanceForm.tsx
./app/attendance/components/forms/PrayerDetailsForm.tsx
./app/attendance/components/forms/ServiceAttendanceForm.tsx
./app/attendance/components/forms/ServiceDetailsForm.tsx
./app/attendance/components/forms/VisitorsForm.tsx
./app/attendance/page.tsx
./app/attendance/record/page.tsx
./app/attendance/utils/attendanceUtils.ts
./app/auth/admin/login/page.tsx
./app/auth/admin/register/page.tsx
./app/auth/login/page.tsx
./app/auth/logout/page.tsx
./app/auth/member/login/page.tsx
./app/auth/register/page.tsx
./app/auth/reset-password/page.tsx
./app/auth/signout/route.ts
./app/cell-groups/[id]/members/page.tsx
./app/cell-groups/[id]/page.tsx
./app/cell-groups/add/page.tsx
./app/cell-groups/dashboard/page.tsx
./app/cell-groups/page.tsx
./app/classes/[id]/edit/page.tsx
./app/classes/[id]/levels/[levelId]/enroll/page.tsx
./app/classes/[id]/levels/[levelId]/page.tsx
./app/classes/[id]/levels/[levelId]/sessions/[sessionId]/attendance/page.tsx
./app/classes/[id]/levels/[levelId]/sessions/add/page.tsx
./app/classes/[id]/levels/add/page.tsx
./app/classes/[id]/levels/layout.tsx
./app/classes/[id]/page.tsx
./app/classes/[id]/sessions/add/page.tsx
./app/classes/[id]/sessions/layout.tsx
./app/classes/add/page.tsx
./app/classes/layout.tsx
./app/classes/page.tsx
./app/components/CellGroupForm.tsx
./app/components/DistrictForm.tsx
./app/components/Header.tsx
./app/components/MemberForm.tsx
./app/components/MemberTokenManager.tsx
./app/components/ProtectedRoute.tsx
./app/components/QRCodeGenerator.tsx
./app/components/QRCodeScanner.tsx
./app/components/RealtimeAttendanceStats.tsx
./app/components/RichTextEditor.tsx
./app/components/layout/Header.tsx
./app/components/layout/Layout.tsx
./app/components/layout/MobileOptimization.tsx
./app/components/layout/Sidebar.tsx
./app/components/ui/Badge.tsx
./app/components/ui/Breadcrumb.tsx
./app/components/ui/Button.tsx
./app/components/ui/Card.tsx
./app/components/ui/Input.tsx
./app/components/ui/Pagination.tsx
./app/components/ui/Select.tsx
./app/components/ui/Table.tsx
./app/contexts/AuthContext.tsx
./app/dashboard/layout.tsx
./app/dashboard/page.tsx
./app/districts/[id]/page.tsx
./app/districts/add/page.tsx
./app/districts/page.tsx
./app/layout.tsx
./app/lib/role-utils.ts
./app/lib/supabase.ts
./app/member/attendance/page.tsx
./app/member/cell-group/page.tsx
./app/member/classes/page.tsx
./app/member/dashboard/page.tsx
./app/member/layout.tsx
./app/member/news/[id]/page.tsx
./app/member/news/page.tsx
./app/member/profile/page.tsx
./app/member/reset-password/page.tsx
./app/members/[id]/page.tsx
./app/members/add/page.tsx
./app/members/edit/[id]/page.tsx
./app/members/page.tsx
./app/ministries/[id]/members/page.tsx
./app/ministries/[id]/page.tsx
./app/ministries/add/page.tsx
./app/ministries/components/MinistryForm.tsx
./app/ministries/dashboard/page.tsx
./app/ministries/edit/[id]/page.tsx
./app/ministries/page.tsx
./app/page.tsx
./app/pastoral/page.tsx
./app/scan/page.tsx
./app/self-checkin/page.tsx
./app/types/class.ts
./app/types/ministry.ts
./app/types/supabase.ts
./app/utils/passwordUtils.ts
