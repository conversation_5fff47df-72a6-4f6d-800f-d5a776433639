"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const AttendanceController_1 = require("../controllers/AttendanceController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const attendanceController = new AttendanceController_1.AttendanceController();
router.get('/meetings', auth_1.authenticateToken, attendanceController.getMeetings);
router.get('/meetings/:id', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], attendanceController.getMeetingById);
router.post('/meetings', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)('event_category').isIn(['cell_group', 'ministry', 'service', 'prayer', 'class', 'other']),
    (0, express_validator_1.body)('meeting_date').isISO8601(),
    (0, express_validator_1.body)('meeting_type').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('topic').optional().trim(),
    (0, express_validator_1.body)('notes').optional().trim(),
    (0, express_validator_1.body)('location').optional().trim(),
    (0, express_validator_1.body)('offering').optional().isFloat({ min: 0 }),
    (0, express_validator_1.body)('cell_group_id').optional().isUUID(),
    (0, express_validator_1.body)('ministry_id').optional().isUUID(),
    (0, express_validator_1.body)('participants').isArray(),
    (0, express_validator_1.body)('participants.*.member_id').isUUID(),
    (0, express_validator_1.body)('participants.*.status').isIn(['present', 'absent', 'late']),
    (0, express_validator_1.body)('visitors').optional().isArray(),
    (0, express_validator_1.body)('visitors.*.name').isLength({ min: 1 }).trim(),
], attendanceController.createMeeting);
router.put('/meetings/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('meeting_date').optional().isISO8601(),
    (0, express_validator_1.body)('meeting_type').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('topic').optional().trim(),
    (0, express_validator_1.body)('notes').optional().trim(),
    (0, express_validator_1.body)('location').optional().trim(),
    (0, express_validator_1.body)('offering').optional().isFloat({ min: 0 }),
], attendanceController.updateMeeting);
router.delete('/meetings/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
], attendanceController.deleteMeeting);
router.get('/meetings/:id/participants', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], attendanceController.getMeetingParticipants);
router.put('/meetings/:id/participants', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('participants').isArray(),
    (0, express_validator_1.body)('participants.*.member_id').isUUID(),
    (0, express_validator_1.body)('participants.*.status').isIn(['present', 'absent', 'late']),
], attendanceController.updateMeetingParticipants);
router.get('/meetings/:id/visitors', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], attendanceController.getMeetingVisitors);
router.post('/meetings/:id/visitors', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('visitors').isArray(),
    (0, express_validator_1.body)('visitors.*.name').isLength({ min: 1 }).trim(),
], attendanceController.addMeetingVisitors);
router.get('/stats', [
    auth_1.authenticateToken,
    (0, express_validator_1.query)('start_date').optional().isISO8601(),
    (0, express_validator_1.query)('end_date').optional().isISO8601(),
    (0, express_validator_1.query)('event_category').optional().isIn(['cell_group', 'ministry', 'service', 'prayer', 'class', 'other']),
], attendanceController.getAttendanceStats);
router.get('/members/:memberId/history', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('memberId').isUUID(),
], attendanceController.getMemberAttendanceHistory);
router.patch('/meetings/:id/realtime', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('is_realtime').isBoolean(),
], attendanceController.toggleMeetingRealtime);
exports.default = router;
//# sourceMappingURL=attendance.js.map