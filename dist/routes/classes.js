"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const ClassController_1 = require("../controllers/ClassController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const classController = new ClassController_1.ClassController();
router.get('/', auth_1.authenticateToken, classController.getClasses);
router.get('/:id', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], classController.getClassById);
router.post('/', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.body)('name').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('description').optional().trim(),
    (0, express_validator_1.body)('category').isIn(['bible_study', 'counseling', 'discipleship', 'leadership', 'other']),
    (0, express_validator_1.body)('max_students').optional().isInt({ min: 1 }),
    (0, express_validator_1.body)('has_levels').isBoolean(),
], classController.createClass);
router.put('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
], classController.updateClass);
router.delete('/:id', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
], classController.deleteClass);
router.get('/:id/levels', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], classController.getClassLevels);
router.post('/:id/levels', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('name').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('description').optional().trim(),
    (0, express_validator_1.body)('order_number').isInt({ min: 1 }),
], classController.createClassLevel);
router.get('/:id/sessions', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], classController.getClassSessions);
router.post('/:id/sessions', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('title').isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('session_date').isISO8601(),
    (0, express_validator_1.body)('start_time').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    (0, express_validator_1.body)('end_time').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
], classController.createClassSession);
router.get('/:id/enrollments', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('id').isUUID(),
], classController.getClassEnrollments);
router.post('/:id/enrollments', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.body)('member_id').isUUID(),
    (0, express_validator_1.body)('level_id').optional().isUUID(),
], classController.enrollMember);
router.put('/:id/enrollments/:enrollmentId', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('id').isUUID(),
    (0, express_validator_1.param)('enrollmentId').isUUID(),
    (0, express_validator_1.body)('status').isIn(['enrolled', 'completed', 'dropped']),
], classController.updateEnrollment);
router.get('/levels/:levelId', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('levelId').isUUID(),
], classController.getLevelById);
router.put('/levels/:levelId', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('levelId').isUUID(),
], classController.updateLevel);
router.delete('/levels/:levelId', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('levelId').isUUID(),
], classController.deleteLevel);
router.get('/sessions/:sessionId', [
    auth_1.authenticateToken,
    (0, express_validator_1.param)('sessionId').isUUID(),
], classController.getSessionById);
router.put('/sessions/:sessionId', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('sessionId').isUUID(),
], classController.updateSession);
router.delete('/sessions/:sessionId', [
    auth_1.authenticateToken,
    auth_1.requireAdmin,
    (0, express_validator_1.param)('sessionId').isUUID(),
], classController.deleteSession);
exports.default = router;
//# sourceMappingURL=classes.js.map