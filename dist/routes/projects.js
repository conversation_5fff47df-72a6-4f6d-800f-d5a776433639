"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const ProjectController_1 = require("../controllers/ProjectController");
const router = (0, express_1.Router)();
router.get('/published', ProjectController_1.getProjects);
router.use(auth_1.authenticateToken);
router.get('/', ProjectController_1.getProjects);
router.get('/:id', ProjectController_1.getProject);
router.use(auth_1.requireAdmin);
router.post('/', ProjectController_1.createProject);
router.put('/:id', ProjectController_1.updateProject);
router.delete('/:id', ProjectController_1.deleteProject);
exports.default = router;
//# sourceMappingURL=projects.js.map