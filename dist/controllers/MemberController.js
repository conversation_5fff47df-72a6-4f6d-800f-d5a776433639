"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemberController = void 0;
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
const password_1 = require("../utils/password");
class MemberController {
    async getMembers(req, res, next) {
        try {
            const { page = 1, limit = 10, search } = req.query;
            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);
            const where = { status: 'active' };
            if (search) {
                where.OR = [
                    { first_name: { contains: search, mode: 'insensitive' } },
                    { last_name: { contains: search, mode: 'insensitive' } },
                    { email: { contains: search, mode: 'insensitive' } },
                ];
            }
            const [members, total] = await Promise.all([
                database_1.prisma.member.findMany({
                    where,
                    select: {
                        id: true,
                        email: true,
                        first_name: true,
                        last_name: true,
                        phone: true,
                        status: true,
                        role: true,
                        join_date: true,
                        cell_group: { select: { id: true, name: true } },
                        district: { select: { id: true, name: true } },
                    },
                    orderBy: { last_name: 'asc' },
                    skip,
                    take,
                }),
                database_1.prisma.member.count({ where }),
            ]);
            res.json({
                success: true,
                data: members,
                pagination: { page, limit: take, total, totalPages: Math.ceil(total / take) },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getMemberById(req, res, next) {
        try {
            const { id } = req.params;
            const member = await database_1.prisma.member.findUnique({
                where: { id },
                select: {
                    id: true, email: true, first_name: true, last_name: true,
                    phone: true, address: true, date_of_birth: true, gender: true,
                    marital_status: true, join_date: true, status: true, role: true,
                    cell_group: { select: { id: true, name: true } },
                    district: { select: { id: true, name: true } },
                },
            });
            if (!member)
                throw (0, errorHandler_1.createError)('Member not found', 404);
            res.json({ success: true, data: member });
        }
        catch (error) {
            next(error);
        }
    }
    async createMember(req, res, next) {
        try {
            const memberData = req.body;
            const existingMember = await database_1.prisma.member.findUnique({
                where: { email: memberData.email },
            });
            if (existingMember)
                throw (0, errorHandler_1.createError)('Email already exists', 400);
            const defaultPassword = (0, password_1.generateDefaultPassword)(memberData.date_of_birth);
            const hashedPassword = await (0, password_1.hashPassword)(defaultPassword);
            const member = await database_1.prisma.member.create({
                data: {
                    ...memberData,
                    password_hash: hashedPassword,
                    password_reset_required: true,
                    date_of_birth: memberData.date_of_birth ? new Date(memberData.date_of_birth) : null,
                },
            });
            res.status(201).json({ success: true, data: member });
        }
        catch (error) {
            next(error);
        }
    }
    async updateMember(req, res, next) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const member = await database_1.prisma.member.update({
                where: { id },
                data: {
                    ...updateData,
                    date_of_birth: updateData.date_of_birth ? new Date(updateData.date_of_birth) : undefined,
                },
            });
            res.json({ success: true, data: member });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteMember(req, res, next) {
        try {
            const { id } = req.params;
            await database_1.prisma.member.update({
                where: { id },
                data: { status: 'inactive' },
            });
            res.json({ success: true, data: { message: 'Member deleted successfully' } });
        }
        catch (error) {
            next(error);
        }
    }
    async getMemberAttendance(req, res, next) {
        try {
            const { id } = req.params;
            const attendance = await database_1.prisma.attendanceParticipant.findMany({
                where: { member_id: id },
                include: {
                    meeting: {
                        select: {
                            meeting_date: true, meeting_type: true, topic: true,
                            cell_group: { select: { name: true } },
                        },
                    },
                },
                orderBy: { meeting: { meeting_date: 'desc' } },
            });
            res.json({ success: true, data: attendance });
        }
        catch (error) {
            next(error);
        }
    }
    async setMemberPassword(req, res, next) {
        try {
            const { id } = req.params;
            const { password } = req.body;
            const hashedPassword = await (0, password_1.hashPassword)(password);
            await database_1.prisma.member.update({
                where: { id },
                data: { password_hash: hashedPassword, password_reset_required: true },
            });
            res.json({ success: true, data: { message: 'Password set successfully' } });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.MemberController = MemberController;
//# sourceMappingURL=MemberController.js.map