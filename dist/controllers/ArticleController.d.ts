import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare class ArticleController {
    getArticles(req: Request, res: Response, next: NextFunction): Promise<void>;
    getArticleById(req: Request, res: Response, next: NextFunction): Promise<void>;
    createArticle(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateArticle(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteArticle(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    publishArticle(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    unpublishArticle(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    toggleFeatured(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    incrementViewCount(req: Request, res: Response, next: NextFunction): Promise<void>;
    getFeaturedArticles(req: Request, res: Response, next: NextFunction): Promise<void>;
    getArticlesByCategory(req: Request, res: Response, next: NextFunction): Promise<void>;
    getCategories(req: Request, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=ArticleController.d.ts.map