"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClassController = void 0;
const express_validator_1 = require("express-validator");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
class ClassController {
    async getClasses(req, res, next) {
        try {
            const { page = 1, limit = 10, search, category, status } = req.query;
            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);
            const where = {};
            if (search) {
                where.OR = [
                    { name: { contains: search, mode: 'insensitive' } },
                    { description: { contains: search, mode: 'insensitive' } },
                ];
            }
            if (category) {
                where.category = category;
            }
            if (status) {
                where.status = status;
            }
            const [classes, total] = await Promise.all([
                database_1.prisma.class.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        category: true,
                        max_students: true,
                        status: true,
                        has_levels: true,
                        created_at: true,
                        _count: {
                            select: {
                                levels: true,
                                enrollments: { where: { status: 'enrolled' } },
                                sessions: true,
                            },
                        },
                    },
                    orderBy: { name: 'asc' },
                    skip,
                    take,
                }),
                database_1.prisma.class.count({ where }),
            ]);
            res.json({
                success: true,
                data: classes,
                pagination: { page, limit: take, total, totalPages: Math.ceil(total / take) },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getClassById(req, res, next) {
        try {
            const { id } = req.params;
            const classData = await database_1.prisma.class.findUnique({
                where: { id },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    category: true,
                    max_students: true,
                    status: true,
                    has_levels: true,
                    created_at: true,
                    updated_at: true,
                    levels: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            order_number: true,
                            _count: {
                                select: { enrollments: { where: { status: 'enrolled' } } },
                            },
                        },
                        orderBy: { order_number: 'asc' },
                    },
                    sessions: {
                        select: {
                            id: true,
                            title: true,
                            session_date: true,
                            start_time: true,
                            end_time: true,
                            location: true,
                            order_number: true,
                            instructor: {
                                select: { first_name: true, last_name: true },
                            },
                        },
                        orderBy: { order_number: 'asc' },
                    },
                    _count: {
                        select: {
                            enrollments: { where: { status: 'enrolled' } },
                        },
                    },
                },
            });
            if (!classData) {
                throw (0, errorHandler_1.createError)('Class not found', 404);
            }
            res.json({ success: true, data: classData });
        }
        catch (error) {
            next(error);
        }
    }
    async createClass(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const classData = req.body;
            const newClass = await database_1.prisma.class.create({
                data: classData,
            });
            res.status(201).json({ success: true, data: newClass });
        }
        catch (error) {
            next(error);
        }
    }
    async updateClass(req, res, next) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const updatedClass = await database_1.prisma.class.update({
                where: { id },
                data: updateData,
            });
            res.json({ success: true, data: updatedClass });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteClass(req, res, next) {
        try {
            const { id } = req.params;
            await database_1.prisma.class.delete({
                where: { id },
            });
            res.json({ success: true, data: { message: 'Class deleted successfully' } });
        }
        catch (error) {
            next(error);
        }
    }
    async getClassLevels(req, res, next) {
        try {
            const { id } = req.params;
            const levels = await database_1.prisma.classLevel.findMany({
                where: { class_id: id },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    order_number: true,
                    prerequisite_level: {
                        select: { id: true, name: true },
                    },
                    _count: {
                        select: { enrollments: { where: { status: 'enrolled' } } },
                    },
                },
                orderBy: { order_number: 'asc' },
            });
            res.json({ success: true, data: levels });
        }
        catch (error) {
            next(error);
        }
    }
    async createClassLevel(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const levelData = { ...req.body, class_id: id };
            const level = await database_1.prisma.classLevel.create({
                data: levelData,
            });
            res.status(201).json({ success: true, data: level });
        }
        catch (error) {
            next(error);
        }
    }
    async getClassSessions(req, res, next) {
        try {
            const { id } = req.params;
            const sessions = await database_1.prisma.classSession.findMany({
                where: { class_id: id },
                select: {
                    id: true,
                    title: true,
                    description: true,
                    session_date: true,
                    start_time: true,
                    end_time: true,
                    location: true,
                    order_number: true,
                    instructor: {
                        select: { id: true, first_name: true, last_name: true },
                    },
                    level: {
                        select: { id: true, name: true },
                    },
                },
                orderBy: { order_number: 'asc' },
            });
            res.json({ success: true, data: sessions });
        }
        catch (error) {
            next(error);
        }
    }
    async createClassSession(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const sessionData = {
                ...req.body,
                class_id: id,
                session_date: new Date(req.body.session_date),
            };
            const session = await database_1.prisma.classSession.create({
                data: sessionData,
            });
            res.status(201).json({ success: true, data: session });
        }
        catch (error) {
            next(error);
        }
    }
    async getClassEnrollments(req, res, next) {
        try {
            const { id } = req.params;
            const enrollments = await database_1.prisma.classEnrollment.findMany({
                where: { class_id: id },
                select: {
                    id: true,
                    status: true,
                    enrollment_date: true,
                    completion_date: true,
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                        },
                    },
                    level: {
                        select: { id: true, name: true },
                    },
                },
                orderBy: { enrollment_date: 'desc' },
            });
            res.json({ success: true, data: enrollments });
        }
        catch (error) {
            next(error);
        }
    }
    async enrollMember(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { id } = req.params;
            const { member_id, level_id } = req.body;
            const existingEnrollment = await database_1.prisma.classEnrollment.findUnique({
                where: {
                    class_id_member_id: {
                        class_id: id,
                        member_id,
                    },
                },
            });
            if (existingEnrollment) {
                throw (0, errorHandler_1.createError)('Member is already enrolled in this class', 400);
            }
            const enrollment = await database_1.prisma.classEnrollment.create({
                data: {
                    class_id: id,
                    member_id,
                    level_id,
                },
            });
            res.status(201).json({ success: true, data: enrollment });
        }
        catch (error) {
            next(error);
        }
    }
    async updateEnrollment(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { enrollmentId } = req.params;
            const { status } = req.body;
            const updateData = { status };
            if (status === 'completed') {
                updateData.completion_date = new Date();
            }
            const enrollment = await database_1.prisma.classEnrollment.update({
                where: { id: enrollmentId },
                data: updateData,
            });
            res.json({ success: true, data: enrollment });
        }
        catch (error) {
            next(error);
        }
    }
    async getLevelById(req, res, next) {
        try {
            const { levelId } = req.params;
            const level = await database_1.prisma.classLevel.findUnique({
                where: { id: levelId },
                include: {
                    class: { select: { id: true, name: true } },
                    prerequisite_level: { select: { id: true, name: true } },
                    sessions: {
                        select: {
                            id: true,
                            title: true,
                            session_date: true,
                            start_time: true,
                            end_time: true,
                        },
                        orderBy: { order_number: 'asc' },
                    },
                },
            });
            if (!level) {
                throw (0, errorHandler_1.createError)('Level not found', 404);
            }
            res.json({ success: true, data: level });
        }
        catch (error) {
            next(error);
        }
    }
    async updateLevel(req, res, next) {
        try {
            const { levelId } = req.params;
            const updateData = req.body;
            const level = await database_1.prisma.classLevel.update({
                where: { id: levelId },
                data: updateData,
            });
            res.json({ success: true, data: level });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteLevel(req, res, next) {
        try {
            const { levelId } = req.params;
            await database_1.prisma.classLevel.delete({
                where: { id: levelId },
            });
            res.json({ success: true, data: { message: 'Level deleted successfully' } });
        }
        catch (error) {
            next(error);
        }
    }
    async getSessionById(req, res, next) {
        try {
            const { sessionId } = req.params;
            const session = await database_1.prisma.classSession.findUnique({
                where: { id: sessionId },
                include: {
                    class: { select: { id: true, name: true } },
                    level: { select: { id: true, name: true } },
                    instructor: { select: { id: true, first_name: true, last_name: true } },
                    attendance_meeting: {
                        select: {
                            id: true,
                            _count: { select: { participants: true } },
                        },
                    },
                },
            });
            if (!session) {
                throw (0, errorHandler_1.createError)('Session not found', 404);
            }
            res.json({ success: true, data: session });
        }
        catch (error) {
            next(error);
        }
    }
    async updateSession(req, res, next) {
        try {
            const { sessionId } = req.params;
            const updateData = req.body;
            if (updateData.session_date) {
                updateData.session_date = new Date(updateData.session_date);
            }
            const session = await database_1.prisma.classSession.update({
                where: { id: sessionId },
                data: updateData,
            });
            res.json({ success: true, data: session });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteSession(req, res, next) {
        try {
            const { sessionId } = req.params;
            await database_1.prisma.classSession.delete({
                where: { id: sessionId },
            });
            res.json({ success: true, data: { message: 'Session deleted successfully' } });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.ClassController = ClassController;
//# sourceMappingURL=ClassController.js.map