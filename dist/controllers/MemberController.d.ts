import { Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare class MemberController {
    getMembers(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMemberById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createMember(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateMember(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteMember(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMemberAttendance(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    setMemberPassword(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=MemberController.d.ts.map