"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const express_validator_1 = require("express-validator");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
const password_1 = require("../utils/password");
const jwt_1 = require("../utils/jwt");
class AuthController {
    async adminLogin(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { email, password } = req.body;
            const user = await database_1.prisma.member.findUnique({
                where: { email },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    password_hash: true,
                    role: true,
                    role_level: true,
                    status: true,
                },
            });
            if (!user || user.status !== 'active') {
                throw (0, errorHandler_1.createError)('Invalid credentials', 401);
            }
            if (user.role !== 'admin' && user.role_level < 4) {
                throw (0, errorHandler_1.createError)('Admin access required', 403);
            }
            if (!user.password_hash || !(await (0, password_1.comparePassword)(password, user.password_hash))) {
                throw (0, errorHandler_1.createError)('Invalid credentials', 401);
            }
            const tokenPayload = {
                id: user.id,
                email: user.email,
                role: user.role,
                role_level: user.role_level,
            };
            const accessToken = (0, jwt_1.generateAccessToken)(tokenPayload);
            const refreshToken = (0, jwt_1.generateRefreshToken)(tokenPayload);
            const response = {
                success: true,
                data: {
                    user: {
                        id: user.id,
                        email: user.email,
                        first_name: user.first_name,
                        last_name: user.last_name,
                        role: user.role,
                        role_level: user.role_level,
                    },
                    tokens: {
                        access_token: accessToken,
                        refresh_token: refreshToken,
                    },
                },
            };
            res.json(response);
        }
        catch (error) {
            next(error);
        }
    }
    async memberLogin(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { email, password } = req.body;
            const user = await database_1.prisma.member.findUnique({
                where: { email },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    password_hash: true,
                    password_reset_required: true,
                    role: true,
                    role_level: true,
                    status: true,
                },
            });
            if (!user || user.status !== 'active') {
                throw (0, errorHandler_1.createError)('Invalid credentials', 401);
            }
            if (!user.password_hash) {
                const response = {
                    success: false,
                    error: {
                        message: 'Password not set for this account',
                        code: 'PASSWORD_NOT_SET',
                    },
                };
                return res.status(401).json(response);
            }
            if (!(await (0, password_1.comparePassword)(password, user.password_hash))) {
                throw (0, errorHandler_1.createError)('Invalid credentials', 401);
            }
            const tokenPayload = {
                id: user.id,
                email: user.email,
                role: user.role,
                role_level: user.role_level,
            };
            const accessToken = (0, jwt_1.generateAccessToken)(tokenPayload);
            const refreshToken = (0, jwt_1.generateRefreshToken)(tokenPayload);
            const response = {
                success: true,
                data: {
                    user: {
                        id: user.id,
                        email: user.email,
                        first_name: user.first_name,
                        last_name: user.last_name,
                        role: user.role,
                        role_level: user.role_level,
                    },
                    tokens: {
                        access_token: accessToken,
                        refresh_token: refreshToken,
                    },
                    passwordResetRequired: user.password_reset_required,
                },
            };
            res.json(response);
        }
        catch (error) {
            next(error);
        }
    }
    async checkMember(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { email } = req.body;
            const member = await database_1.prisma.member.findUnique({
                where: { email },
                select: {
                    id: true,
                    email: true,
                    password_hash: true,
                },
            });
            if (!member) {
                throw (0, errorHandler_1.createError)('Member not found', 404);
            }
            const response = {
                success: true,
                data: {
                    memberId: member.id,
                    hasPassword: !!member.password_hash,
                },
            };
            res.json(response);
        }
        catch (error) {
            next(error);
        }
    }
    async resetPassword(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)('Validation failed', 400);
            }
            const { memberId, currentPassword, newPassword } = req.body;
            const member = await database_1.prisma.member.findUnique({
                where: { id: memberId },
                select: {
                    id: true,
                    password_hash: true,
                },
            });
            if (!member) {
                throw (0, errorHandler_1.createError)('Member not found', 404);
            }
            if (!member.password_hash || !(await (0, password_1.comparePassword)(currentPassword, member.password_hash))) {
                throw (0, errorHandler_1.createError)('Current password is incorrect', 400);
            }
            const hashedPassword = await (0, password_1.hashPassword)(newPassword);
            await database_1.prisma.member.update({
                where: { id: memberId },
                data: {
                    password_hash: hashedPassword,
                    password_reset_required: false,
                },
            });
            const response = {
                success: true,
                data: { message: 'Password updated successfully' },
            };
            res.json(response);
        }
        catch (error) {
            next(error);
        }
    }
    async refreshToken(req, res, next) {
        try {
            const { refresh_token } = req.body;
            if (!refresh_token) {
                throw (0, errorHandler_1.createError)('Refresh token required', 401);
            }
            const decoded = (0, jwt_1.verifyRefreshToken)(refresh_token);
            const user = await database_1.prisma.member.findUnique({
                where: { id: decoded.id },
                select: {
                    id: true,
                    email: true,
                    role: true,
                    role_level: true,
                    status: true,
                },
            });
            if (!user || user.status !== 'active') {
                throw (0, errorHandler_1.createError)('User not found or inactive', 401);
            }
            const tokenPayload = {
                id: user.id,
                email: user.email,
                role: user.role,
                role_level: user.role_level,
            };
            const accessToken = (0, jwt_1.generateAccessToken)(tokenPayload);
            const newRefreshToken = (0, jwt_1.generateRefreshToken)(tokenPayload);
            const response = {
                success: true,
                data: {
                    access_token: accessToken,
                    refresh_token: newRefreshToken,
                },
            };
            res.json(response);
        }
        catch (error) {
            next(error);
        }
    }
    async logout(req, res, next) {
        try {
            const response = {
                success: true,
                data: { message: 'Logged out successfully' },
            };
            res.json(response);
        }
        catch (error) {
            next(error);
        }
    }
    async getCurrentUser(req, res, next) {
        try {
            if (!req.user) {
                throw (0, errorHandler_1.createError)('User not found', 404);
            }
            const user = await database_1.prisma.member.findUnique({
                where: { id: req.user.id },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    phone: true,
                    address: true,
                    date_of_birth: true,
                    gender: true,
                    marital_status: true,
                    join_date: true,
                    role: true,
                    role_level: true,
                    cell_group_id: true,
                    district_id: true,
                    cell_group: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    district: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('User not found', 404);
            }
            const response = {
                success: true,
                data: user,
            };
            res.json(response);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=AuthController.js.map