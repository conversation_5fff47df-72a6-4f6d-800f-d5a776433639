"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceController = void 0;
const express_validator_1 = require("express-validator");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
class AttendanceController {
    async getMeetings(req, res, next) {
        try {
            const { page = 1, limit = 10, search, event_category, start_date, end_date, cell_group_id, ministry_id, } = req.query;
            const skip = (page - 1) * limit;
            const take = Math.min(limit, 100);
            const where = {};
            if (search) {
                where.OR = [
                    { topic: { contains: search, mode: "insensitive" } },
                    { meeting_type: { contains: search, mode: "insensitive" } },
                ];
            }
            if (event_category) {
                where.event_category = event_category;
            }
            if (start_date) {
                where.meeting_date = {
                    ...where.meeting_date,
                    gte: new Date(start_date),
                };
            }
            if (end_date) {
                where.meeting_date = {
                    ...where.meeting_date,
                    lte: new Date(end_date),
                };
            }
            if (cell_group_id) {
                where.cell_group_id = cell_group_id;
            }
            if (ministry_id) {
                where.ministry_id = ministry_id;
            }
            const [meetings, total] = await Promise.all([
                database_1.prisma.attendanceMeeting.findMany({
                    where,
                    select: {
                        id: true,
                        event_category: true,
                        meeting_date: true,
                        meeting_type: true,
                        topic: true,
                        location: true,
                        offering: true,
                        is_realtime: true,
                        created_at: true,
                        cell_group: {
                            select: { id: true, name: true },
                        },
                        ministry: {
                            select: { id: true, name: true },
                        },
                        _count: {
                            select: {
                                participants: true,
                                visitors: true,
                            },
                        },
                    },
                    orderBy: { meeting_date: "desc" },
                    skip,
                    take,
                }),
                database_1.prisma.attendanceMeeting.count({ where }),
            ]);
            res.json({
                success: true,
                data: meetings,
                pagination: {
                    page,
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getMeetingById(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const meeting = await database_1.prisma.attendanceMeeting.findUnique({
                where: { id },
                select: {
                    id: true,
                    event_category: true,
                    meeting_date: true,
                    meeting_type: true,
                    topic: true,
                    notes: true,
                    location: true,
                    offering: true,
                    is_realtime: true,
                    created_at: true,
                    updated_at: true,
                    cell_group: {
                        select: { id: true, name: true },
                    },
                    ministry: {
                        select: { id: true, name: true },
                    },
                    participants: {
                        select: {
                            id: true,
                            status: true,
                            notes: true,
                            member: {
                                select: {
                                    id: true,
                                    first_name: true,
                                    last_name: true,
                                    email: true,
                                },
                            },
                        },
                    },
                    visitors: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            phone: true,
                            email: true,
                            notes: true,
                        },
                    },
                },
            });
            if (!meeting) {
                throw (0, errorHandler_1.createError)("Meeting not found", 404);
            }
            res.json({ success: true, data: meeting });
        }
        catch (error) {
            next(error);
        }
    }
    async createMeeting(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { participants, visitors, ...meetingData } = req.body;
            const result = await database_1.prisma.$transaction(async (tx) => {
                const meeting = await tx.attendanceMeeting.create({
                    data: {
                        ...meetingData,
                        meeting_date: new Date(meetingData.meeting_date),
                    },
                });
                if (participants && participants.length > 0) {
                    await tx.attendanceParticipant.createMany({
                        data: participants.map((p) => ({
                            meeting_id: meeting.id,
                            member_id: p.member_id,
                            status: p.status,
                            notes: p.notes,
                        })),
                    });
                }
                if (visitors && visitors.length > 0) {
                    await tx.attendanceVisitor.createMany({
                        data: visitors.map((v) => ({
                            meeting_id: meeting.id,
                            first_name: v.first_name || v.name?.split(" ")[0] || "",
                            last_name: v.last_name ||
                                v.name?.split(" ").slice(1).join(" ") ||
                                "",
                            phone: v.phone,
                            email: v.email,
                            notes: v.notes,
                        })),
                    });
                }
                return meeting;
            });
            if (result.is_realtime) {
                const io = req.app.get("io");
                io.to(`meeting-${result.id}`).emit("attendance-updated", {
                    meetingId: result.id,
                    participantCount: participants?.length || 0,
                    visitorCount: visitors?.length || 0,
                });
            }
            res.status(201).json({ success: true, data: result });
        }
        catch (error) {
            next(error);
        }
    }
    async updateMeeting(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const updateData = req.body;
            const existingMeeting = await database_1.prisma.attendanceMeeting.findUnique({
                where: { id },
            });
            if (!existingMeeting) {
                throw (0, errorHandler_1.createError)("Meeting not found", 404);
            }
            const meeting = await database_1.prisma.attendanceMeeting.update({
                where: { id },
                data: {
                    ...updateData,
                    meeting_date: updateData.meeting_date
                        ? new Date(updateData.meeting_date)
                        : undefined,
                },
            });
            res.json({ success: true, data: meeting });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteMeeting(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const existingMeeting = await database_1.prisma.attendanceMeeting.findUnique({
                where: { id },
            });
            if (!existingMeeting) {
                throw (0, errorHandler_1.createError)("Meeting not found", 404);
            }
            await database_1.prisma.attendanceMeeting.delete({
                where: { id },
            });
            res.json({
                success: true,
                data: { message: "Meeting deleted successfully" },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getMeetingParticipants(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const participants = await database_1.prisma.attendanceParticipant.findMany({
                where: { meeting_id: id },
                select: {
                    id: true,
                    status: true,
                    notes: true,
                    member: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                        },
                    },
                },
                orderBy: {
                    member: { last_name: "asc" },
                },
            });
            res.json({ success: true, data: participants });
        }
        catch (error) {
            next(error);
        }
    }
    async updateMeetingParticipants(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const { participants } = req.body;
            await database_1.prisma.$transaction(async (tx) => {
                await tx.attendanceParticipant.deleteMany({
                    where: { meeting_id: id },
                });
                if (participants && participants.length > 0) {
                    await tx.attendanceParticipant.createMany({
                        data: participants.map((p) => ({
                            meeting_id: id,
                            member_id: p.member_id,
                            status: p.status,
                            notes: p.notes,
                        })),
                    });
                }
            });
            const meeting = await database_1.prisma.attendanceMeeting.findUnique({
                where: { id },
                select: { is_realtime: true },
            });
            if (meeting?.is_realtime) {
                const io = req.app.get("io");
                io.to(`meeting-${id}`).emit("attendance-updated", {
                    meetingId: id,
                    participantCount: participants?.length || 0,
                });
            }
            res.json({
                success: true,
                data: { message: "Participants updated successfully" },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getMeetingVisitors(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const visitors = await database_1.prisma.attendanceVisitor.findMany({
                where: { meeting_id: id },
                select: {
                    id: true,
                    first_name: true,
                    last_name: true,
                    phone: true,
                    email: true,
                    notes: true,
                    created_at: true,
                },
                orderBy: { first_name: "asc" },
            });
            res.json({ success: true, data: visitors });
        }
        catch (error) {
            next(error);
        }
    }
    async addMeetingVisitors(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const { visitors } = req.body;
            await database_1.prisma.attendanceVisitor.createMany({
                data: visitors.map((v) => ({
                    meeting_id: id,
                    first_name: v.first_name || v.name?.split(" ")[0] || "",
                    last_name: v.last_name ||
                        v.name?.split(" ").slice(1).join(" ") ||
                        "",
                    phone: v.phone,
                    email: v.email,
                    notes: v.notes,
                })),
            });
            res.json({
                success: true,
                data: { message: "Visitors added successfully" },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async getAttendanceStats(req, res, next) {
        try {
            const { start_date, end_date, event_category } = req.query;
            const where = {};
            if (start_date) {
                where.meeting_date = {
                    ...where.meeting_date,
                    gte: new Date(start_date),
                };
            }
            if (end_date) {
                where.meeting_date = {
                    ...where.meeting_date,
                    lte: new Date(end_date),
                };
            }
            if (event_category) {
                where.event_category = event_category;
            }
            const [totalMeetings, totalParticipants, totalVisitors, presentCount, absentCount, lateCount,] = await Promise.all([
                database_1.prisma.attendanceMeeting.count({ where }),
                database_1.prisma.attendanceParticipant.count({
                    where: { meeting: where },
                }),
                database_1.prisma.attendanceVisitor.count({
                    where: { meeting: where },
                }),
                database_1.prisma.attendanceParticipant.count({
                    where: {
                        meeting: where,
                        status: "present",
                    },
                }),
                database_1.prisma.attendanceParticipant.count({
                    where: {
                        meeting: where,
                        status: "absent",
                    },
                }),
                database_1.prisma.attendanceParticipant.count({
                    where: {
                        meeting: where,
                        status: "late",
                    },
                }),
            ]);
            const stats = {
                totalMeetings,
                totalParticipants,
                totalVisitors,
                presentCount,
                absentCount,
                lateCount,
                attendanceRate: totalParticipants > 0
                    ? (((presentCount + lateCount) / totalParticipants) *
                        100).toFixed(2)
                    : "0.00",
            };
            res.json({ success: true, data: stats });
        }
        catch (error) {
            next(error);
        }
    }
    async getMemberAttendanceHistory(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { memberId } = req.params;
            const { page = 1, limit = 10 } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const take = Math.min(Number(limit), 100);
            const [attendance, total] = await Promise.all([
                database_1.prisma.attendanceParticipant.findMany({
                    where: { member_id: memberId },
                    select: {
                        id: true,
                        status: true,
                        notes: true,
                        meeting: {
                            select: {
                                id: true,
                                meeting_date: true,
                                meeting_type: true,
                                topic: true,
                                event_category: true,
                                cell_group: {
                                    select: { name: true },
                                },
                                ministry: {
                                    select: { name: true },
                                },
                            },
                        },
                    },
                    orderBy: {
                        meeting: { meeting_date: "desc" },
                    },
                    skip,
                    take,
                }),
                database_1.prisma.attendanceParticipant.count({
                    where: { member_id: memberId },
                }),
            ]);
            res.json({
                success: true,
                data: attendance,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    totalPages: Math.ceil(total / take),
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async toggleMeetingRealtime(req, res, next) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                throw (0, errorHandler_1.createError)("Validation failed", 400);
            }
            const { id } = req.params;
            const { is_realtime } = req.body;
            const meeting = await database_1.prisma.attendanceMeeting.update({
                where: { id },
                data: { is_realtime },
                select: {
                    id: true,
                    is_realtime: true,
                    topic: true,
                },
            });
            res.json({ success: true, data: meeting });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.AttendanceController = AttendanceController;
//# sourceMappingURL=AttendanceController.js.map