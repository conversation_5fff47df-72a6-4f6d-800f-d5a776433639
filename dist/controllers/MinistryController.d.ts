import { Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare class MinistryController {
    getMinistries(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMinistryById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    createMinistry(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateMinistry(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    deleteMinistry(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMinistryMembers(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    addMembersToMinistry(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    updateMinistryMember(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    removeMemberFromMinistry(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    getMinistryMeetings(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=MinistryController.d.ts.map