{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,6CAA0C;AAC1C,iDAA4C;AAC5C,iDAA6C;AAWtC,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAgB,EAChB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAA,0BAAW,EAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,eAAM,CAAC,UAAU,CAAQ,CAAC;QAG5D,MAAM,IAAI,GAAG,MAAM,iBAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;YACzB,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACtC,MAAM,IAAA,0BAAW,EAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;QACvD,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAA,0BAAW,EAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAxCW,QAAA,iBAAiB,qBAwC5B;AAEK,MAAM,YAAY,GAAG,CAC1B,GAAgB,EAChB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAdW,QAAA,YAAY,gBAcvB;AAEK,MAAM,aAAa,GAAG,CAC3B,GAAgB,EAChB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAVW,QAAA,aAAa,iBAUxB"}