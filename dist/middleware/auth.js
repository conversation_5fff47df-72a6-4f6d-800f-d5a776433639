"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireMember = exports.requireAdmin = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config/config");
const database_1 = require("../config/database");
const errorHandler_1 = require("./errorHandler");
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            throw (0, errorHandler_1.createError)('Access token required', 401);
        }
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.JWT_SECRET);
        const user = await database_1.prisma.member.findUnique({
            where: { id: decoded.id },
            select: {
                id: true,
                email: true,
                role: true,
                role_level: true,
                status: true,
            },
        });
        if (!user || user.status !== 'active') {
            throw (0, errorHandler_1.createError)('User not found or inactive', 401);
        }
        req.user = user;
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            next((0, errorHandler_1.createError)('Invalid token', 401));
        }
        else {
            next(error);
        }
    }
};
exports.authenticateToken = authenticateToken;
const requireAdmin = (req, res, next) => {
    if (!req.user) {
        return next((0, errorHandler_1.createError)('Authentication required', 401));
    }
    if (req.user.role !== 'admin' && req.user.role_level < 4) {
        return next((0, errorHandler_1.createError)('Admin access required', 403));
    }
    next();
};
exports.requireAdmin = requireAdmin;
const requireMember = (req, res, next) => {
    if (!req.user) {
        return next((0, errorHandler_1.createError)('Authentication required', 401));
    }
    next();
};
exports.requireMember = requireMember;
//# sourceMappingURL=auth.js.map